/**
 * @Title: ThirdPlatformOrderContoller.java
 * @Package cn.cmcc.plan.controller
 * @Description: 三方平台创建新工单
 * <AUTHOR>
 * @date 2022年3月30日
 * @version V1.0
 */
package cn.cmcc.plan.controller;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import cn.cmcc.common.annotation.TaskApi;
import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.enums.DealStatusEnums;
import cn.cmcc.common.enums.TaskApiType;
import cn.cmcc.common.enums.ThirdBusinessType;
import cn.cmcc.common.interactive.context.InterfaceCallContext;
import cn.cmcc.common.interactive.domain.T5gThirdCall;
import cn.cmcc.common.log.annotation.Log;
import cn.cmcc.common.log.enums.SysOperType;
import cn.cmcc.common.web.controller.BaseController;
import cn.cmcc.plan.service.IThirdPlatformOrderService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/service/third/platform")
public class ThirdPlatformOrderContoller extends BaseController{
	
	@Autowired
	private IThirdPlatformOrderService thirdPlatformOrderService;

	/**
	 * 数据保存
	 * 
	 * @param sitePlan
	 * @return
	 */
	@Log(title = "三方平台创建工单", sysOperType = SysOperType.INSERT)
	@TaskApi(name = "三方平台创建工单(ICOS)",businessCode = ThirdBusinessType.T_CREATE_ORDER,apiType = TaskApiType.THIRD_CALLBACK ,licensekeyCode = "T_CREATE_ORDER_LICENSE")
	@PostMapping(value = "/createOrder")
	@ResponseBody
	public Map<String, Object> createOrder(@RequestBody String text) {
        T5gThirdCall thirdCall = InterfaceCallContext.get();
        
        Map<String, Object> resMap = null;
		try {
			resMap = thirdPlatformOrderService.createOrder(text);
			thirdCall.setBusinessId(MapUtils.getString(resMap, "REQUIRE_id", ""));
			thirdCall.setSiteIdCmcc(MapUtils.getString(resMap, "JZXQ_id", ""));
			if(Constants.SUCCESS.equals(resMap.get("code"))){
				thirdCall.setStatus(DealStatusEnums.SUCCESS.getCode());
			}
			return resMap;
		} catch (Exception e) {
			log.error("三方平台创建工单 error:",e);
			resMap = new HashMap<String, Object>();
			resMap.put("code", Constants.FAIL); 
			resMap.put("desc", ExceptionUtils.getMessage(e));
			return resMap;
		} finally {
			thirdCall.setResponse(JSONObject.toJSONString(resMap));
		}
	}

	/**
	 * 生成工单
	 *
	 * @param message 请求消息
	 * @return 结果
	 */
	@Log(title = "三方平台生成工单", sysOperType = SysOperType.INSERT)
	@PostMapping(value = "/generateOrder")
	@TaskApi(name = "三方平台创建工单",businessCode = ThirdBusinessType.T_CREATE_ORDER,apiType = TaskApiType.THIRD_CALLBACK)
	@ResponseBody
	public Map<String, Object> generateOrder(@RequestBody String message) {
		T5gThirdCall record = InterfaceCallContext.get();

		Map<String, Object> result = null;
		String id = null;
		try {
			JSONObject jsonObject = JSON.parseObject(message);
			id = jsonObject.getString("ID");

			result = thirdPlatformOrderService.generateOrder(jsonObject);
			record.setStatus(DealStatusEnums.SUCCESS.getCode());

			Object orderNo = result.remove("orderNo"), checkResult = result.remove("checkResult");
			if (orderNo != null) record.setSiteIdCmcc(orderNo.toString());
			if (checkResult != null) record.setCheckResult(checkResult.toString());
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
			record.setCheckFailReason(ex.getMessage());

			result = new HashMap<>();
			result.put("ID", id);
			result.put("Result", "失败");
		} finally {
			record.setBusinessId(id);
			record.setResponse(JSONObject.toJSONString(result));
		}
		return result;
	}
	
}
