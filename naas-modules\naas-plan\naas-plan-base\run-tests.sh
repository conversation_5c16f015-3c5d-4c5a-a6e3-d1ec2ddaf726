#!/bin/bash

echo "========================================"
echo "StorageService 测试运行脚本"
echo "========================================"

echo ""
echo "1. 运行单元测试（推荐，快速）"
echo "mvn test -Dtest=StorageServiceUnitTest"
mvn test -Dtest=StorageServiceUnitTest

echo ""
echo "2. 运行增强重试功能测试（无Spring Boot依赖）"
echo "mvn test -Dtest=StorageServiceEnhancedRetryTest"
mvn test -Dtest=StorageServiceEnhancedRetryTest

echo ""
echo "3. 运行性能测试（无Spring Boot依赖）"
echo "mvn test -Dtest=StorageServicePerformanceTest"
mvn test -Dtest=StorageServicePerformanceTest

echo ""
echo "========================================"
echo "测试完成！"
echo "========================================"
