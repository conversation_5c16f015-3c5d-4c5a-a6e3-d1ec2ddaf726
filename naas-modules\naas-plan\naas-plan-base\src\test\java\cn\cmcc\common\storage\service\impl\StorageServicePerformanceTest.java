package cn.cmcc.common.storage.service.impl;

import cn.cmcc.common.storage.config.S3Properties;
import cn.cmcc.common.storage.service.EnhancedRetryExecutor;
import cn.cmcc.common.storage.service.RetryMetricsService;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StorageService 性能测试类
 * 测试大文件上传下载、并发重试、压力测试等场景
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class StorageServicePerformanceTest {

    @Mock
    private AmazonS3 mockS3Client;

    @Mock
    private S3Properties mockS3Properties;

    @Mock
    private EnhancedRetryExecutor mockRetryExecutor;

    @Mock
    private RetryMetricsService mockMetricsService;

    @Mock
    private HttpServletResponse mockResponse;

    @Mock
    private S3Object mockS3Object;

    @Mock
    private S3ObjectInputStream mockInputStream;

    @Mock
    private ObjectMetadata mockObjectMetadata;

    @Mock
    private MultipartFile mockMultipartFile;

    @InjectMocks
    private StorageService storageService;

    private ExecutorService executorService;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        when(mockS3Properties.getMaxRetries()).thenReturn(3);
        when(mockS3Properties.getBucketName()).thenReturn("integration-test-bucket");
        when(mockS3Properties.getConnectionTimeout()).thenReturn(10000);
        when(mockS3Properties.getSocketTimeout()).thenReturn(50000);
        when(mockS3Properties.getRequestTimeout()).thenReturn(300000);

        // 使用反射设置私有字段
        ReflectionTestUtils.setField(storageService, "client", mockS3Client);

        // 创建线程池
        executorService = Executors.newFixedThreadPool(20);
    }

    // ==================== 大文件性能测试 ====================

    @Test
    void testLargeFileUploadRetry_Performance() throws Exception {
        // 模拟100MB文件
        byte[] largeFileData = createLargeFileData(100 * 1024 * 1024);
        when(mockMultipartFile.getBytes()).thenReturn(largeFileData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("large-file.dat");

        PutObjectResult mockResult = new PutObjectResult();
        mockResult.setContentMd5("large-file-md5");

        // 模拟第一次失败，第二次成功
        when(mockRetryExecutor.executeWithRetry(eq("upload"), any(), anyString()))
                .thenReturn(mockResult);

        long startTime = System.currentTimeMillis();

        // 执行测试
        String result = storageService.upload(mockMultipartFile);

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("large-file.dat"));

        // 性能验证：大文件上传应该在合理时间内完成（这里设置为10秒）
        assertTrue(duration < 10000, "大文件上传耗时过长: " + duration + "ms");

        // 验证内存使用情况（通过GC前后的内存差异）
        System.gc();
        long memoryBefore = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();

        // 再次执行上传
        storageService.upload(mockMultipartFile);

        System.gc();
        long memoryAfter = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();

        // 内存增长不应该超过文件大小的2倍（考虑到缓冲区等开销）
        long memoryIncrease = memoryAfter - memoryBefore;
        assertTrue(memoryIncrease < largeFileData.length * 2,
                "内存使用过多: " + memoryIncrease + " bytes");
    }

    @Test
    void testLargeFileDownloadRetry_Performance() throws Exception {
        // 模拟大文件下载
        byte[] largeFileData = createLargeFileData(50 * 1024 * 1024);
        ByteArrayInputStream largeInputStream = new ByteArrayInputStream(largeFileData);

        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);
        when(mockResponse.getOutputStream()).thenReturn(createMockServletOutputStream());
        when(mockResponse.isCommitted()).thenReturn(false);

        // 模拟输入流读取
        doAnswer(invocation -> {
            byte[] buffer = invocation.getArgument(0);
            // 模拟分块读取
            int chunkSize = Math.min(buffer.length, 8192);
            System.arraycopy(largeFileData, 0, buffer, 0, chunkSize);
            return chunkSize;
        }).when(mockInputStream).read(any(byte[].class));

        long startTime = System.currentTimeMillis();

        // 执行测试
        storageService.downLoad("large-file.dat", mockResponse);

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 性能验证：大文件下载应该在合理时间内完成
        assertTrue(duration < 15000, "大文件下载耗时过长: " + duration + "ms");

        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    // ==================== 并发性能测试 ====================

    @Test
    void testConcurrentUploadRetry_Performance() throws Exception {
        int concurrentRequests = 50;
        CountDownLatch latch = new CountDownLatch(concurrentRequests);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        AtomicLong totalDuration = new AtomicLong(0);

        // 准备测试数据
        byte[] testData = createTestFileData(1024); // 1KB文件
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("concurrent-test.txt");

        PutObjectResult mockResult = new PutObjectResult();
        mockResult.setContentMd5("concurrent-md5");
        when(mockRetryExecutor.executeWithRetry(eq("upload"), any(), anyString()))
                .thenReturn(mockResult);

        long startTime = System.currentTimeMillis();

        // 启动并发请求
        for (int i = 0; i < concurrentRequests; i++) {
            final int requestId = i;
            executorService.submit(() -> {
                try {
                    long requestStart = System.currentTimeMillis();
                    String result = storageService.upload(mockMultipartFile);
                    long requestEnd = System.currentTimeMillis();

                    totalDuration.addAndGet(requestEnd - requestStart);
                    successCount.incrementAndGet();

                    assertNotNull(result);
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                    System.err.println("并发请求 " + requestId + " 失败: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有请求完成
        assertTrue(latch.await(30, TimeUnit.SECONDS), "并发测试超时");

        long endTime = System.currentTimeMillis();
        long totalTestDuration = endTime - startTime;

        // 性能验证
        assertEquals(concurrentRequests, successCount.get(), "并发请求成功数量不符");
        assertEquals(0, failureCount.get(), "不应该有失败的请求");

        double averageRequestDuration = (double) totalDuration.get() / concurrentRequests;
        double throughput = (double) concurrentRequests / totalTestDuration * 1000; // 请求/秒

        System.out.println("并发上传性能统计:");
        System.out.println("- 总请求数: " + concurrentRequests);
        System.out.println("- 成功请求数: " + successCount.get());
        System.out.println("- 总耗时: " + totalTestDuration + "ms");
        System.out.println("- 平均请求耗时: " + averageRequestDuration + "ms");
        System.out.println("- 吞吐量: " + throughput + " 请求/秒");

        // 性能阈值验证
        assertTrue(averageRequestDuration < 1000, "平均请求耗时过长: " + averageRequestDuration + "ms");
        assertTrue(throughput > 10, "吞吐量过低: " + throughput + " 请求/秒");
    }

    @Test
    void testConcurrentDownloadRetry_Performance() throws Exception {
        int concurrentRequests = 30;
        CountDownLatch latch = new CountDownLatch(concurrentRequests);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicLong totalDuration = new AtomicLong(0);

        // 准备测试数据
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);

        long startTime = System.currentTimeMillis();

        // 启动并发下载请求
        for (int i = 0; i < concurrentRequests; i++) {
            final int requestId = i;
            executorService.submit(() -> {
                try {
                    long requestStart = System.currentTimeMillis();
                    OutputStream testOutputStream = new ByteArrayOutputStream();
                    storageService.downLoad("concurrent-download-" + requestId + ".txt", testOutputStream);
                    long requestEnd = System.currentTimeMillis();

                    totalDuration.addAndGet(requestEnd - requestStart);
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    System.err.println("并发下载请求 " + requestId + " 失败: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有请求完成
        assertTrue(latch.await(20, TimeUnit.SECONDS), "并发下载测试超时");

        long endTime = System.currentTimeMillis();
        long totalTestDuration = endTime - startTime;

        // 性能验证
        assertEquals(concurrentRequests, successCount.get(), "并发下载请求成功数量不符");

        double averageRequestDuration = (double) totalDuration.get() / concurrentRequests;
        double throughput = (double) concurrentRequests / totalTestDuration * 1000;

        System.out.println("并发下载性能统计:");
        System.out.println("- 总请求数: " + concurrentRequests);
        System.out.println("- 成功请求数: " + successCount.get());
        System.out.println("- 平均请求耗时: " + averageRequestDuration + "ms");
        System.out.println("- 吞吐量: " + throughput + " 请求/秒");

        assertTrue(averageRequestDuration < 2000, "平均下载请求耗时过长: " + averageRequestDuration + "ms");
        assertTrue(throughput > 5, "下载吞吐量过低: " + throughput + " 请求/秒");
    }

    // ==================== 压力测试 ====================

    @Test
    void testHighConcurrencyRetry_StressTest() throws Exception {
        int highConcurrency = 100;
        CountDownLatch latch = new CountDownLatch(highConcurrency);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger retryCount = new AtomicInteger(0);

        // 模拟部分请求需要重试
        when(mockRetryExecutor.executeWithRetry(eq("upload"), any(), anyString()))
                .thenAnswer(invocation -> {
                    // 30%的请求模拟重试
                    if (Math.random() < 0.3) {
                        retryCount.incrementAndGet();
                        // 模拟重试延迟
                        Thread.sleep(100);
                    }
                    PutObjectResult result = new PutObjectResult();
                    result.setContentMd5("stress-test-md5");
                    return result;
                });

        byte[] testData = createTestFileData(512); // 512字节文件
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("stress-test.txt");

        long startTime = System.currentTimeMillis();

        // 启动高并发请求
        for (int i = 0; i < highConcurrency; i++) {
            executorService.submit(() -> {
                try {
                    String result = storageService.upload(mockMultipartFile);
                    assertNotNull(result);
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    System.err.println("压力测试请求失败: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有请求完成
        assertTrue(latch.await(60, TimeUnit.SECONDS), "压力测试超时");

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.println("压力测试统计:");
        System.out.println("- 并发数: " + highConcurrency);
        System.out.println("- 成功请求数: " + successCount.get());
        System.out.println("- 重试请求数: " + retryCount.get());
        System.out.println("- 总耗时: " + duration + "ms");
        System.out.println("- 成功率: " + (double) successCount.get() / highConcurrency * 100 + "%");

        // 压力测试验证
        assertTrue(successCount.get() >= highConcurrency * 0.95, "成功率过低");
        assertTrue(duration < 30000, "压力测试耗时过长: " + duration + "ms");
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建大文件测试数据
     */
    private byte[] createLargeFileData(int sizeInBytes) {
        byte[] data = new byte[sizeInBytes];
        // 填充一些模式数据而不是全零，更接近真实场景
        for (int i = 0; i < sizeInBytes; i++) {
            data[i] = (byte) (i % 256);
        }
        return data;
    }

    /**
     * 创建测试文件数据
     */
    private byte[] createTestFileData(int sizeInBytes) {
        return ("Test file content for performance testing. ".repeat(sizeInBytes / 40))
                .getBytes();
    }

    /**
     * 创建模拟的ServletOutputStream
     */
    private ServletOutputStream createMockServletOutputStream() {
        return new ServletOutputStream() {
            private final ByteArrayOutputStream buffer = new ByteArrayOutputStream();

            @Override
            public void write(int b) throws IOException {
                buffer.write(b);
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setWriteListener(javax.servlet.WriteListener writeListener) {
                // Mock implementation
            }
        };
    }
}
