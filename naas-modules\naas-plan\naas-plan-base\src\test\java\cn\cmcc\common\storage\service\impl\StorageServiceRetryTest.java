package cn.cmcc.common.storage.service.impl;

import cn.cmcc.common.storage.config.S3Properties;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * StorageService 上传下载重试功能测试类
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class StorageServiceRetryTest {

    @Mock
    private AmazonS3 mockS3Client;

    @Mock
    private S3Properties mockS3Properties;

    @Mock
    private HttpServletResponse mockResponse;

    @Mock
    private S3Object mockS3Object;

    @Mock
    private S3ObjectInputStream mockInputStream;

    @Mock
    private ObjectMetadata mockObjectMetadata;

    @Mock
    private MultipartFile mockMultipartFile;

    @InjectMocks
    private StorageService storageService;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        when(mockS3Properties.getMaxRetries()).thenReturn(3);
        when(mockS3Properties.getBucketName()).thenReturn("test-bucket");
        when(mockS3Properties.getConnectionTimeout()).thenReturn(10000);
        when(mockS3Properties.getSocketTimeout()).thenReturn(50000);
        when(mockS3Properties.getRequestTimeout()).thenReturn(300000);

        // 使用反射设置私有字段 client
        ReflectionTestUtils.setField(storageService, "client", mockS3Client);
    }

    // ==================== 上传重试测试 ====================

    @Test
    void testUploadWithRetry_Success() throws Exception {
        // 准备测试数据
        byte[] testData = "test file content".getBytes();
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("test.txt");

        PutObjectResult mockResult = new PutObjectResult();
        mockResult.setContentMd5("test-md5");
        when(mockS3Client.putObject(any(PutObjectRequest.class))).thenReturn(mockResult);

        // 执行测试
        String result = storageService.upload(mockMultipartFile);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("test.txt"));
        verify(mockS3Client, times(1)).putObject(any(PutObjectRequest.class));
    }

    @Test
    void testUploadWithRetry_NetworkErrorThenSuccess() throws Exception {
        // 准备测试数据
        byte[] testData = "test file content".getBytes();
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("test.txt");

        PutObjectResult mockResult = new PutObjectResult();
        mockResult.setContentMd5("test-md5");

        // 第一次调用抛出网络异常，第二次成功
        when(mockS3Client.putObject(any(PutObjectRequest.class)))
            .thenThrow(new SdkClientException("Connection timeout"))
            .thenReturn(mockResult);

        // 执行测试
        String result = storageService.upload(mockMultipartFile);

        // 验证结果
        assertNotNull(result);
        verify(mockS3Client, times(2)).putObject(any(PutObjectRequest.class));
    }

    @Test
    void testUploadWithRetry_MaxRetriesExceeded() throws Exception {
        // 准备测试数据
        byte[] testData = "test file content".getBytes();
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("test.txt");

        // 所有调用都抛出网络异常
        when(mockS3Client.putObject(any(PutObjectRequest.class)))
            .thenThrow(new SdkClientException("Connection timeout"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            storageService.upload(mockMultipartFile);
        });

        assertTrue(exception.getMessage().contains("文件上传失败"));
        // 验证重试了最大次数 + 1 次（初始尝试 + 3次重试）
        verify(mockS3Client, times(4)).putObject(any(PutObjectRequest.class));
    }

    @Test
    void testUploadWithRetry_NonRetryableError() throws Exception {
        // 准备测试数据
        byte[] testData = "test file content".getBytes();
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("test.txt");

        // 抛出AWS服务异常（不可重试）
        when(mockS3Client.putObject(any(PutObjectRequest.class)))
            .thenThrow(new AmazonServiceException("Access denied"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            storageService.upload(mockMultipartFile);
        });

        assertTrue(exception.getMessage().contains("文件上传失败"));
        // 验证只调用了1次，没有重试
        verify(mockS3Client, times(1)).putObject(any(PutObjectRequest.class));
    }

    // ==================== 下载重试测试 ====================

    @Test
    void testDownloadWithRetry_Success() throws Exception {
        // 准备测试数据
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockS3Client.getObject(anyString(), anyString())).thenReturn(mockS3Object);
        when(mockResponse.getOutputStream()).thenReturn(mock(ServletOutputStream.class));
        when(mockResponse.isCommitted()).thenReturn(false);

        // 执行测试
        storageService.downLoad("test-file.txt", mockResponse);

        // 验证结果
        verify(mockS3Client, times(1)).getObject(anyString(), anyString());
        verify(mockS3Object, times(1)).getObjectContent();
    }

    @Test
    void testDownloadWithRetry_NetworkErrorThenSuccess() throws Exception {
        // 准备测试数据
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockResponse.getOutputStream()).thenReturn(mock(ServletOutputStream.class));
        when(mockResponse.isCommitted()).thenReturn(false);

        // 第一次调用抛出网络异常，第二次成功
        when(mockS3Client.getObject(anyString(), anyString()))
            .thenThrow(new SdkClientException("Connection timeout"))
            .thenReturn(mockS3Object);

        // 执行测试
        storageService.downLoad("test-file.txt", mockResponse);

        // 验证重试了2次
        verify(mockS3Client, times(2)).getObject(anyString(), anyString());
    }

    @Test
    void testDownloadWithRetry_MaxRetriesExceeded() throws Exception {
        // 所有调用都抛出网络异常
        when(mockS3Client.getObject(anyString(), anyString()))
            .thenThrow(new SdkClientException("Connection timeout"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            storageService.downLoad("test-file.txt", mockResponse);
        });

        assertTrue(exception.getMessage().contains("响应流下载失败"));
        // 验证重试了最大次数 + 1 次
        verify(mockS3Client, times(4)).getObject(anyString(), anyString());
    }

    @Test
    void testDownloadWithRetry_FileNotFound() throws Exception {
        // 抛出AWS服务异常（文件不存在，不可重试）
        when(mockS3Client.getObject(anyString(), anyString()))
            .thenThrow(new AmazonServiceException("NoSuchKey"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            storageService.downLoad("non-existent-file.txt", mockResponse);
        });

        assertTrue(exception.getMessage().contains("响应流下载失败"));
        // 验证只调用了1次，没有重试
        verify(mockS3Client, times(1)).getObject(anyString(), anyString());
    }

    // ==================== 异常处理测试 ====================

    @Test
    void testClientDisconnectException_NoRetry() throws Exception {
        // 模拟客户端断开连接的IOException
        when(mockS3Client.getObject(anyString(), anyString())).thenReturn(mockS3Object);
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockResponse.getOutputStream()).thenReturn(mock(ServletOutputStream.class));
        when(mockResponse.isCommitted()).thenReturn(false);

        // 模拟在复制数据时发生客户端断开连接异常
        doThrow(new IOException("Broken pipe")).when(mockInputStream).read(any(byte[].class));

        // 执行测试 - 应该不抛出异常，只是记录日志
        assertDoesNotThrow(() -> {
            storageService.downLoad("test-file.txt", mockResponse);
        });

        // 验证只调用了1次getObject，没有重试
        verify(mockS3Client, times(1)).getObject(anyString(), anyString());
    }

    // ==================== 边界条件测试 ====================

    @Test
    void testRetryDelay_ExponentialBackoff() throws Exception {
        // 这个测试验证重试延迟的指数退避算法
        // 由于calculateRetryDelay是私有方法，我们通过观察重试行为来验证

        byte[] testData = "test".getBytes();
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("test.txt");

        // 设置较少的重试次数以加快测试
        when(mockS3Properties.getMaxRetries()).thenReturn(2);

        when(mockS3Client.putObject(any(PutObjectRequest.class)))
            .thenThrow(new SdkClientException("Connection timeout"));

        long startTime = System.currentTimeMillis();

        assertThrows(RuntimeException.class, () -> {
            storageService.upload(mockMultipartFile);
        });

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证总时间包含了重试延迟（至少1秒 + 2秒的延迟）
        assertTrue(duration >= 3000, "重试延迟时间应该符合指数退避策略");
        verify(mockS3Client, times(3)).putObject(any(PutObjectRequest.class));
    }

    @Test
    void testDownloadToOutputStream_Success() throws Exception {
        // 准备测试数据
        OutputStream testOutputStream = createTestOutputStream();
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockS3Client.getObject(anyString(), anyString())).thenReturn(mockS3Object);

        // 执行测试
        storageService.downLoad("test-file.txt", testOutputStream);

        // 验证结果
        verify(mockS3Client, times(1)).getObject(anyString(), anyString());
        verify(mockS3Object, times(1)).getObjectContent();
    }

    @Test
    void testDownloadToOutputStream_NetworkErrorThenSuccess() throws Exception {
        // 准备测试数据
        OutputStream testOutputStream = createTestOutputStream();
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);

        // 第一次调用抛出网络异常，第二次成功
        when(mockS3Client.getObject(anyString(), anyString()))
            .thenThrow(new SdkClientException("Read timeout"))
            .thenReturn(mockS3Object);

        // 执行测试
        storageService.downLoad("test-file.txt", testOutputStream);

        // 验证重试了2次
        verify(mockS3Client, times(2)).getObject(anyString(), anyString());
    }

    @Test
    void testDownloadToLocalFile_Success() throws Exception {
        // 准备测试数据
        String localFilePath = "test-local-file.txt";
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockS3Client.getObject(anyString(), anyString())).thenReturn(mockS3Object);

        // 执行测试
        storageService.downLoad("test-file.txt", localFilePath);

        // 验证结果
        verify(mockS3Client, times(1)).getObject(anyString(), anyString());
        verify(mockS3Object, times(1)).getObjectContent();
    }

    @Test
    void testDownloadToLocalFile_NetworkErrorThenSuccess() throws Exception {
        // 准备测试数据
        String localFilePath = "test-local-file.txt";
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);

        // 第一次调用抛出网络异常，第二次成功
        when(mockS3Client.getObject(anyString(), anyString()))
            .thenThrow(new SdkClientException("Socket timeout"))
            .thenReturn(mockS3Object);

        // 执行测试
        storageService.downLoad("test-file.txt", localFilePath);

        // 验证重试了2次
        verify(mockS3Client, times(2)).getObject(anyString(), anyString());
    }

    @Test
    void testGetObjectMetadata_Success() throws Exception {
        // 准备测试数据
        when(mockS3Object.getObjectMetadata()).thenReturn(mockObjectMetadata);
        when(mockS3Client.getObject(anyString(), anyString())).thenReturn(mockS3Object);

        // 执行测试
        ObjectMetadata result = storageService.getObjectMetadata("/test-bucket/test-file.txt");

        // 验证结果
        assertNotNull(result);
        verify(mockS3Client, times(1)).getObject(anyString(), anyString());
        verify(mockS3Object, times(1)).getObjectMetadata();
    }

    @Test
    void testGetObjectMetadata_NetworkErrorThenSuccess() throws Exception {
        // 准备测试数据
        when(mockS3Object.getObjectMetadata()).thenReturn(mockObjectMetadata);

        // 第一次调用抛出网络异常，第二次成功
        when(mockS3Client.getObject(anyString(), anyString()))
            .thenThrow(new SdkClientException("Connection refused"))
            .thenReturn(mockS3Object);

        // 执行测试
        ObjectMetadata result = storageService.getObjectMetadata("/test-bucket/test-file.txt");

        // 验证结果
        assertNotNull(result);
        verify(mockS3Client, times(2)).getObject(anyString(), anyString());
    }

    @Test
    void testCheckFileDownloadable_Success() throws Exception {
        // 准备测试数据
        when(mockObjectMetadata.getContentLength()).thenReturn(1024L);
        when(mockS3Object.getObjectMetadata()).thenReturn(mockObjectMetadata);
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockS3Client.getObject(anyString(), anyString())).thenReturn(mockS3Object);
        when(mockInputStream.read(any(byte[].class))).thenReturn(100);

        // 执行测试
        boolean result = storageService.checkFileDownloadable("test-file.txt");

        // 验证结果
        assertTrue(result);
        verify(mockS3Client, times(1)).getObject(anyString(), anyString());
    }

    @Test
    void testCheckFileDownloadable_NetworkErrorThenSuccess() throws Exception {
        // 准备测试数据
        when(mockObjectMetadata.getContentLength()).thenReturn(1024L);
        when(mockS3Object.getObjectMetadata()).thenReturn(mockObjectMetadata);
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockInputStream.read(any(byte[].class))).thenReturn(100);

        // 第一次调用抛出网络异常，第二次成功
        when(mockS3Client.getObject(anyString(), anyString()))
            .thenThrow(new SdkClientException("Network is unreachable"))
            .thenReturn(mockS3Object);

        // 执行测试
        boolean result = storageService.checkFileDownloadable("test-file.txt");

        // 验证结果
        assertTrue(result);
        verify(mockS3Client, times(2)).getObject(anyString(), anyString());
    }

    @Test
    void testCheckFileDownloadable_FileNotExists() throws Exception {
        // 文件不存在的情况
        when(mockS3Client.getObject(anyString(), anyString()))
            .thenThrow(new AmazonServiceException("NoSuchKey"));

        // 执行测试
        boolean result = storageService.checkFileDownloadable("non-existent-file.txt");

        // 验证结果
        assertFalse(result);
        verify(mockS3Client, times(1)).getObject(anyString(), anyString());
    }

    @Test
    void testCheckFileDownloadable_EmptyFile() throws Exception {
        // 准备测试数据 - 文件大小为0
        when(mockObjectMetadata.getContentLength()).thenReturn(0L);
        when(mockS3Object.getObjectMetadata()).thenReturn(mockObjectMetadata);
        when(mockS3Client.getObject(anyString(), anyString())).thenReturn(mockS3Object);

        // 执行测试
        boolean result = storageService.checkFileDownloadable("empty-file.txt");

        // 验证结果
        assertFalse(result);
        verify(mockS3Client, times(1)).getObject(anyString(), anyString());
    }

    // ==================== 并发和中断测试 ====================

    @Test
    void testUploadInterrupted() throws Exception {
        // 准备测试数据
        byte[] testData = "test".getBytes();
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("test.txt");

        // 模拟线程中断
        Thread.currentThread().interrupt();

        when(mockS3Client.putObject(any(PutObjectRequest.class)))
            .thenThrow(new SdkClientException("Connection timeout"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            storageService.upload(mockMultipartFile);
        });

        assertTrue(exception.getMessage().contains("上传被中断"));

        // 清除中断状态
        Thread.interrupted();
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的输出流
     */
    private OutputStream createTestOutputStream() {
        return new ByteArrayOutputStream();
    }

    /**
     * 创建测试用的文件内容
     */
    private byte[] createTestFileContent() {
        return "This is test file content for retry testing".getBytes();
    }
}
