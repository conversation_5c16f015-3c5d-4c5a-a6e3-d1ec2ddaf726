# 测试环境配置 - naas-plan-base 模块
# 引用主配置文件中的存储配置

spring:
  profiles:
    include: storage-enhanced
  
  # 测试数据源配置（如果需要）
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: false

# 存储服务测试配置
s3:
  endpoint: ${S3_ENDPOINT:http://localhost:9000}
  accessKey: ${S3_ACCESS_KEY:minioadmin}
  secretKey: ${S3_SECRET_KEY:minioadmin}
  region: ${S3_REGION:us-east-1}
  https: ${S3_HTTPS:false}
  maxConnections: 50
  bucketName: ${S3_BUCKET_NAME:test-bucket}
  
  # 连接超时配置
  connectionTimeout: 5000
  socketTimeout: 10000
  requestTimeout: 30000
  
  # 测试重试配置
  retry:
    default:
      maxRetries: 2
      baseDelay: 500
      maxDelay: 5000
      strategy: EXPONENTIAL_BACKOFF
      enableJitter: true
      jitterFactor: 0.1
    
    operationSpecific:
      upload:
        maxRetries: 3
        baseDelay: 1000
        maxDelay: 10000
        strategy: EXPONENTIAL_BACKOFF
      
      download:
        maxRetries: 2
        baseDelay: 500
        maxDelay: 5000
        strategy: LINEAR_BACKOFF
      
      metadata:
        maxRetries: 1
        baseDelay: 200
        maxDelay: 2000
        strategy: FIXED_DELAY
        fixedDelay: 1000

# 日志配置
logging:
  level:
    cn.cmcc.common.storage: DEBUG
    cn.cmcc.plan: DEBUG
    com.amazonaws: WARN
    org.apache.http: WARN
    root: INFO

# 测试配置
test:
  timeout:
    upload: 30000
    download: 20000
    concurrent: 60000
  
  data:
    smallFileSize: 1024
    mediumFileSize: 102400
    largeFileSize: 1048576
  
  concurrency:
    maxThreads: 10
    defaultConcurrency: 5
    highConcurrency: 20
