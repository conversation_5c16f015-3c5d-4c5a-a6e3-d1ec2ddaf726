# 测试环境配置 - naas-plan-base 模块

spring:
  # 禁用不需要的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# 存储服务测试配置
s3:
  endpoint: ${S3_ENDPOINT:http://10.18.18.37:9010}
  accessKey: ${S3_ACCESS_KEY:cmcc_minio}
  secretKey: ${S3_SECRET_KEY:cmcc_minio}
  region: ${S3_REGION:us-east-1}
  https: ${S3_HTTPS:false}
  maxConnections: 50
  bucketName: ${S3_BUCKET_NAME:integration-test-bucket}

  # 连接超时配置
  connectionTimeout: 5000
  socketTimeout: 10000
  requestTimeout: 30000

  # 测试重试配置
  retry:
    default:
      maxRetries: 2
      baseDelay: 500
      maxDelay: 5000
      strategy: EXPONENTIAL_BACKOFF
      enableJitter: true
      jitterFactor: 0.1

    operationSpecific:
      upload:
        maxRetries: 3
        baseDelay: 1000
        maxDelay: 10000
        strategy: EXPONENTIAL_BACKOFF

      download:
        maxRetries: 2
        baseDelay: 500
        maxDelay: 5000
        strategy: LINEAR_BACKOFF

      metadata:
        maxRetries: 1
        baseDelay: 200
        maxDelay: 2000
        strategy: FIXED_DELAY
        fixedDelay: 1000

# 日志配置
logging:
  level:
    cn.cmcc.common.storage: DEBUG
    cn.cmcc.plan: DEBUG
    com.amazonaws: WARN
    org.apache.http: WARN
    root: INFO

# 测试配置
test:
  timeout:
    upload: 30000
    download: 20000
    concurrent: 60000

  data:
    smallFileSize: 1024
    mediumFileSize: 102400
    largeFileSize: 1048576

  concurrency:
    maxThreads: 10
    defaultConcurrency: 5
    highConcurrency: 20
