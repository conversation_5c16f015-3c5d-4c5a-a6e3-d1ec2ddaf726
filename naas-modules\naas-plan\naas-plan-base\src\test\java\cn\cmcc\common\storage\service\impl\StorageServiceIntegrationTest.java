package cn.cmcc.common.storage.service.impl;

import cn.cmcc.common.storage.config.RetryConfig;
import cn.cmcc.common.storage.config.S3Properties;
import cn.cmcc.common.storage.service.RetryMetricsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.io.ByteArrayOutputStream;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * StorageService 集成测试类
 * 使用真实的MinIO或AWS S3环境进行测试
 *
 * 运行条件：需要设置环境变量 INTEGRATION_TEST_ENABLED=true
 * 并配置真实的S3服务端点
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
@SpringBootTest(classes = cn.cmcc.common.storage.StorageTestApplication.class)
@ActiveProfiles("integration-test")
@TestPropertySource(locations = "classpath:application-integration-test.yml")
@EnabledIfEnvironmentVariable(named = "INTEGRATION_TEST_ENABLED", matches = "true")
class StorageServiceIntegrationTest {

    @Autowired
    private StorageService storageService;

    @Autowired
    private RetryMetricsService metricsService;

    @Autowired
    private RetryConfig retryConfig;

    @Autowired
    private S3Properties s3Properties;

    private ExecutorService executorService;

    @BeforeEach
    void setUp() {
        // 重置指标
        metricsService.resetMetrics();

        // 创建线程池用于并发测试
        executorService = Executors.newFixedThreadPool(10);

        // 验证配置
        assertNotNull(s3Properties.getEndpoint(), "S3端点未配置");
        assertNotNull(s3Properties.getBucketName(), "S3存储桶未配置");

        System.out.println("集成测试配置:");
        System.out.println("- S3端点: " + s3Properties.getEndpoint());
        System.out.println("- 存储桶: " + s3Properties.getBucketName());
        System.out.println("- 最大重试次数: " + retryConfig.getDefaultSettings().getMaxRetries());
    }

    // ==================== 基础功能集成测试 ====================

    @Test
    void testRealS3Upload_Success() throws Exception {
        // 准备测试文件
        String fileName = "integration-test-upload.txt";
        String fileContent = "This is integration test content for upload functionality.";
        MockMultipartFile testFile = new MockMultipartFile(
                "file", fileName, "text/plain", fileContent.getBytes());

        // 执行上传
        String fileUrl = storageService.upload(testFile);

        // 验证结果
        assertNotNull(fileUrl, "上传应该返回文件URL");
        assertTrue(fileUrl.contains(fileName), "文件URL应该包含文件名");

        System.out.println("上传成功，文件URL: " + fileUrl);

        // 验证指标
        RetryMetricsService.GlobalMetrics globalMetrics = metricsService.getGlobalMetrics();
        System.out.println("上传指标 - 总请求数: " + globalMetrics.getTotalRetries().sum());
        System.out.println("上传指标 - 成功数: " + globalMetrics.getSuccessfulRetries().sum());
    }

    @Test
    void testRealS3Download_Success() throws Exception {
        // 先上传一个文件
        String fileName = "integration-test-download.txt";
        String fileContent = "This is integration test content for download functionality.";
        MockMultipartFile testFile = new MockMultipartFile(
                "file", fileName, "text/plain", fileContent.getBytes());

        String fileUrl = storageService.upload(testFile);
        assertNotNull(fileUrl);

        // 测试下载到输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        storageService.downLoad(fileUrl, outputStream);

        // 验证下载内容
        String downloadedContent = outputStream.toString();
        assertEquals(fileContent, downloadedContent, "下载的内容应该与上传的内容一致");

        System.out.println("下载成功，内容长度: " + downloadedContent.length());

        // 验证指标
        double downloadSuccessRate = metricsService.getSuccessRate("download");
        System.out.println("下载成功率: " + downloadSuccessRate * 100 + "%");
        assertTrue(downloadSuccessRate >= 1.0, "下载成功率应该为100%");
    }

    @Test
    void testRealS3FileMetadata_Success() throws Exception {
        // 先上传一个文件
        String fileName = "integration-test-metadata.txt";
        String fileContent = "This is integration test content for metadata functionality.";
        MockMultipartFile testFile = new MockMultipartFile(
                "file", fileName, "text/plain", fileContent.getBytes());

        String fileUrl = storageService.upload(testFile);
        assertNotNull(fileUrl);

        // 测试获取文件元数据
        var metadata = storageService.getObjectMetadata(fileUrl);

        // 验证元数据
        assertNotNull(metadata, "应该能获取到文件元数据");
        assertEquals(fileContent.length(), metadata.getContentLength(), "文件大小应该一致");

        System.out.println("元数据获取成功:");
        System.out.println("- 文件大小: " + metadata.getContentLength() + " bytes");
        System.out.println("- 内容类型: " + metadata.getContentType());
        System.out.println("- 最后修改时间: " + metadata.getLastModified());
    }

    @Test
    void testRealS3FileDownloadable_Success() throws Exception {
        // 先上传一个文件
        String fileName = "integration-test-downloadable.txt";
        String fileContent = "This is integration test content for downloadable check.";
        MockMultipartFile testFile = new MockMultipartFile(
                "file", fileName, "text/plain", fileContent.getBytes());

        String fileUrl = storageService.upload(testFile);
        assertNotNull(fileUrl);

        // 测试检查文件可下载性
        boolean isDownloadable = storageService.checkFileDownloadable(fileUrl);

        // 验证结果
        assertTrue(isDownloadable, "上传的文件应该是可下载的");

        System.out.println("文件可下载性检查通过");

        // 测试不存在的文件
        boolean nonExistentDownloadable = storageService.checkFileDownloadable("non-existent-file.txt");
        assertFalse(nonExistentDownloadable, "不存在的文件应该不可下载");
    }

    // ==================== 重试机制集成测试 ====================

    @Test
    void testRetryMechanismWithRealNetwork() throws Exception {
        // 这个测试需要在网络不稳定的环境中运行，或者临时修改S3端点来模拟网络问题

        // 准备测试文件
        String fileName = "integration-test-retry.txt";
        String fileContent = "This is integration test content for retry mechanism.";
        MockMultipartFile testFile = new MockMultipartFile(
                "file", fileName, "text/plain", fileContent.getBytes());

        long startTime = System.currentTimeMillis();

        try {
            // 执行上传（可能会触发重试）
            String fileUrl = storageService.upload(testFile);
            assertNotNull(fileUrl);

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            System.out.println("重试机制测试完成，耗时: " + duration + "ms");

            // 检查是否有重试发生
            RetryMetricsService.OperationMetrics uploadMetrics = metricsService.getOperationMetrics("upload");
            if (uploadMetrics != null && uploadMetrics.getTotalRetries().sum() > 0) {
                System.out.println("检测到重试:");
                System.out.println("- 总重试次数: " + uploadMetrics.getTotalRetries().sum());
                System.out.println("- 成功重试次数: " + uploadMetrics.getSuccessfulRetries().sum());
                System.out.println("- 平均耗时: " + uploadMetrics.getAverageDuration() + "ms");
                System.out.println("- 成功率: " + uploadMetrics.getSuccessRate() * 100 + "%");
            }

        } catch (Exception e) {
            // 如果重试后仍然失败，检查重试指标
            RetryMetricsService.OperationMetrics uploadMetrics = metricsService.getOperationMetrics("upload");
            if (uploadMetrics != null) {
                System.out.println("重试失败统计:");
                System.out.println("- 总重试次数: " + uploadMetrics.getTotalRetries().sum());
                System.out.println("- 失败重试次数: " + uploadMetrics.getFailedRetries().sum());
                System.out.println("- 最大重试次数: " + uploadMetrics.getMaxRetryAttempts().get());
            }
            throw e;
        }
    }

    // ==================== 并发集成测试 ====================

    @Test
    void testConcurrentOperationsWithRealS3() throws Exception {
        int concurrentOperations = 10;
        CountDownLatch latch = new CountDownLatch(concurrentOperations);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        long startTime = System.currentTimeMillis();

        // 启动并发上传
        for (int i = 0; i < concurrentOperations; i++) {
            final int operationId = i;
            executorService.submit(() -> {
                try {
                    String fileName = "concurrent-integration-test-" + operationId + ".txt";
                    String fileContent = "Concurrent integration test content " + operationId;
                    MockMultipartFile testFile = new MockMultipartFile(
                            "file", fileName, "text/plain", fileContent.getBytes());

                    // 上传文件
                    String fileUrl = storageService.upload(testFile);
                    assertNotNull(fileUrl);

                    // 立即下载验证
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    storageService.downLoad(fileUrl, outputStream);
                    String downloadedContent = outputStream.toString();
                    assertEquals(fileContent, downloadedContent);

                    successCount.incrementAndGet();
                    System.out.println("并发操作 " + operationId + " 成功完成");

                } catch (Exception e) {
                    failureCount.incrementAndGet();
                    System.err.println("并发操作 " + operationId + " 失败: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有操作完成
        assertTrue(latch.await(60, TimeUnit.SECONDS), "并发集成测试超时");

        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;

        // 验证结果
        System.out.println("并发集成测试结果:");
        System.out.println("- 总操作数: " + concurrentOperations);
        System.out.println("- 成功数: " + successCount.get());
        System.out.println("- 失败数: " + failureCount.get());
        System.out.println("- 总耗时: " + totalDuration + "ms");
        System.out.println("- 成功率: " + (double) successCount.get() / concurrentOperations * 100 + "%");

        // 验证成功率
        assertTrue(successCount.get() >= concurrentOperations * 0.9,
                "并发操作成功率应该至少90%");

        // 打印最终指标统计
        printFinalMetrics();
    }

    // ==================== 配置验证测试 ====================

    @Test
    void testRetryConfigurationEffectiveness() {
        // 验证重试配置是否正确加载
        assertNotNull(retryConfig.getDefaultSettings(), "默认重试配置应该存在");

        RetryConfig.RetrySettings uploadSettings = retryConfig.getSettingsForOperation("upload");
        RetryConfig.RetrySettings downloadSettings = retryConfig.getSettingsForOperation("download");

        System.out.println("重试配置验证:");
        System.out.println("- 上传最大重试次数: " + uploadSettings.getMaxRetries());
        System.out.println("- 上传基础延迟: " + uploadSettings.getBaseDelay() + "ms");
        System.out.println("- 上传重试策略: " + uploadSettings.getStrategy());
        System.out.println("- 下载最大重试次数: " + downloadSettings.getMaxRetries());
        System.out.println("- 下载基础延迟: " + downloadSettings.getBaseDelay() + "ms");
        System.out.println("- 下载重试策略: " + downloadSettings.getStrategy());

        // 验证配置合理性
        assertTrue(uploadSettings.getMaxRetries() > 0, "上传重试次数应该大于0");
        assertTrue(downloadSettings.getMaxRetries() > 0, "下载重试次数应该大于0");
        assertTrue(uploadSettings.getBaseDelay() > 0, "上传基础延迟应该大于0");
        assertTrue(downloadSettings.getBaseDelay() > 0, "下载基础延迟应该大于0");
    }

    // ==================== 辅助方法 ====================

    /**
     * 打印最终的指标统计
     */
    private void printFinalMetrics() {
        System.out.println("\n=== 最终指标统计 ===");

        RetryMetricsService.GlobalMetrics globalMetrics = metricsService.getGlobalMetrics();
        System.out.println("全局指标:");
        System.out.println("- 总重试次数: " + globalMetrics.getTotalRetries().sum());
        System.out.println("- 成功重试次数: " + globalMetrics.getSuccessfulRetries().sum());
        System.out.println("- 失败重试次数: " + globalMetrics.getFailedRetries().sum());
        System.out.println("- 全局成功率: " + globalMetrics.getGlobalSuccessRate() * 100 + "%");
        System.out.println("- 全局平均耗时: " + globalMetrics.getGlobalAverageDuration() + "ms");

        // 按操作类型统计
        RetryMetricsService.OperationMetrics uploadMetrics = metricsService.getOperationMetrics("upload");
        if (uploadMetrics != null) {
            System.out.println("\n上传操作指标:");
            System.out.println("- 重试次数: " + uploadMetrics.getTotalRetries().sum());
            System.out.println("- 成功率: " + uploadMetrics.getSuccessRate() * 100 + "%");
            System.out.println("- 平均耗时: " + uploadMetrics.getAverageDuration() + "ms");
            System.out.println("- 最大重试次数: " + uploadMetrics.getMaxRetryAttempts().get());
        }

        RetryMetricsService.OperationMetrics downloadMetrics = metricsService.getOperationMetrics("download");
        if (downloadMetrics != null) {
            System.out.println("\n下载操作指标:");
            System.out.println("- 重试次数: " + downloadMetrics.getTotalRetries().sum());
            System.out.println("- 成功率: " + downloadMetrics.getSuccessRate() * 100 + "%");
            System.out.println("- 平均耗时: " + downloadMetrics.getAverageDuration() + "ms");
            System.out.println("- 最大重试次数: " + downloadMetrics.getMaxRetryAttempts().get());
        }
    }
}
