-- 创建通用查询编码权限表
CREATE TABLE IF NOT EXISTS t_query_code_permission (
    id VARCHAR(64) PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL,
    username VARCHAR(100) NOT NULL,
    query_code VARCHAR(200) NOT NULL,
    permission_type VARCHAR(20) DEFAULT 'read',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(64),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64),
    remark VARCHAR(500)
);

-- 添加表注释
COMMENT ON TABLE t_query_code_permission IS '通用查询编码权限表';
COMMENT ON COLUMN t_query_code_permission.id IS '主键ID';
COMMENT ON COLUMN t_query_code_permission.user_id IS '用户ID';
COMMENT ON COLUMN t_query_code_permission.username IS '用户名';
COMMENT ON COLUMN t_query_code_permission.query_code IS '查询编码';
COMMENT ON COLUMN t_query_code_permission.permission_type IS '权限类型：read-查询，write-修改';
COMMENT ON COLUMN t_query_code_permission.create_time IS '创建时间';
COMMENT ON COLUMN t_query_code_permission.create_by IS '创建人';
COMMENT ON COLUMN t_query_code_permission.update_time IS '更新时间';
COMMENT ON COLUMN t_query_code_permission.update_by IS '更新人';
COMMENT ON COLUMN t_query_code_permission.remark IS '备注';


-- 创建索引
CREATE INDEX if not exists idx_query_code_permission_user ON t_query_code_permission (username);
CREATE INDEX if not exists idx_query_code_permission_code ON t_query_code_permission (query_code);
CREATE UNIQUE INDEX if not exists uk_query_code_permission ON t_query_code_permission (username, query_code, permission_type);

-- 创建角色-查询编码权限表
CREATE TABLE IF NOT EXISTS t_role_query_code_permission (
    id VARCHAR(64) PRIMARY KEY,
    role_id VARCHAR(64) NOT NULL,
    role_key VARCHAR(100) NOT NULL,
    query_code VARCHAR(200) NOT NULL,
    permission_type VARCHAR(20) DEFAULT 'read',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(64)
);

-- 添加表注释
COMMENT ON TABLE t_role_query_code_permission IS '角色查询编码权限表';
COMMENT ON COLUMN t_role_query_code_permission.id IS '主键ID';
COMMENT ON COLUMN t_role_query_code_permission.role_id IS '角色ID';
COMMENT ON COLUMN t_role_query_code_permission.role_key IS '角色标识';
COMMENT ON COLUMN t_role_query_code_permission.query_code IS '查询编码';
COMMENT ON COLUMN t_role_query_code_permission.permission_type IS '权限类型';
COMMENT ON COLUMN t_role_query_code_permission.create_time IS '创建时间';
COMMENT ON COLUMN t_role_query_code_permission.create_by IS '创建人';

-- 创建索引
CREATE INDEX if not exists idx_role_query_code_permission_role ON t_role_query_code_permission (role_key);
CREATE INDEX if not exists idx_role_query_code_permission_code ON t_role_query_code_permission (query_code);
CREATE UNIQUE INDEX if not exists uk_role_query_code_permission ON t_role_query_code_permission (role_key, query_code, permission_type);


