//package cn.cmcc.common.storage.endpoint;
//
//import cn.cmcc.common.storage.config.RetryConfig;
//import cn.cmcc.common.storage.service.RetryMetricsService;
//import lombok.Data;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.actuator.endpoint.annotation.Endpoint;
//import org.springframework.boot.actuator.endpoint.annotation.ReadOperation;
//import org.springframework.boot.actuator.endpoint.annotation.WriteOperation;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.util.HashMap;
//import java.util.Map;
//
/// **
// * 存储服务重试指标监控端点
// * 提供重试统计信息的查询和管理功能
// *
// * 访问路径：/actuator/storage-retry-metrics
// *
// * <AUTHOR> 4.0 sonnet
// * @date 2025-01-30
// */
//@Component
//@Endpoint(id = "storage-retry-metrics")
//public class StorageRetryMetricsEndpoint {
//
//    @Autowired
//    private RetryMetricsService metricsService;
//
//    @Autowired
//    private RetryConfig retryConfig;
//
//    /**
//     * 获取重试指标信息
//     *
//     * @return 重试指标详情
//     */
//    @ReadOperation
//    public StorageRetryMetricsInfo getRetryMetrics() {
//        StorageRetryMetricsInfo info = new StorageRetryMetricsInfo();
//
//        // 全局指标
//        RetryMetricsService.GlobalMetrics globalMetrics = metricsService.getGlobalMetrics();
//        info.setGlobalMetrics(convertGlobalMetrics(globalMetrics));
//
//        // 按操作类型的指标
//        Map<String, OperationMetricsInfo> operationMetrics = new HashMap<>();
//
//        // 获取上传指标
//        RetryMetricsService.OperationMetrics uploadMetrics = metricsService.getOperationMetrics("upload");
//        if (uploadMetrics != null) {
//            operationMetrics.put("upload", convertOperationMetrics(uploadMetrics));
//        }
//
//        // 获取下载指标
//        RetryMetricsService.OperationMetrics downloadMetrics = metricsService.getOperationMetrics("download");
//        if (downloadMetrics != null) {
//            operationMetrics.put("download", convertOperationMetrics(downloadMetrics));
//        }
//
//        // 获取元数据指标
//        RetryMetricsService.OperationMetrics metadataMetrics = metricsService.getOperationMetrics("metadata");
//        if (metadataMetrics != null) {
//            operationMetrics.put("metadata", convertOperationMetrics(metadataMetrics));
//        }
//
//        info.setOperationMetrics(operationMetrics);
//
//        // 配置信息
//        info.setRetryConfiguration(getRetryConfigurationInfo());
//
//        // 系统信息
//        info.setSystemInfo(getSystemInfo());
//
//        return info;
//    }
//
//    /**
//     * 重置重试指标
//     *
//     * @return 重置结果
//     */
//    @WriteOperation
//    public Map<String, Object> resetMetrics() {
//        metricsService.resetMetrics();
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("status", "success");
//        result.put("message", "重试指标已重置");
//        result.put("resetTime", LocalDateTime.now());
//
//        return result;
//    }
//
//    /**
//     * 转换全局指标
//     */
//    private GlobalMetricsInfo convertGlobalMetrics(RetryMetricsService.GlobalMetrics globalMetrics) {
//        GlobalMetricsInfo info = new GlobalMetricsInfo();
//        info.setTotalRetries(globalMetrics.getTotalRetries().sum());
//        info.setSuccessfulRetries(globalMetrics.getSuccessfulRetries().sum());
//        info.setFailedRetries(globalMetrics.getFailedRetries().sum());
//        info.setGlobalSuccessRate(globalMetrics.getGlobalSuccessRate());
//        info.setGlobalAverageDuration(globalMetrics.getGlobalAverageDuration());
//        return info;
//    }
//
//    /**
//     * 转换操作指标
//     */
//    private OperationMetricsInfo convertOperationMetrics(RetryMetricsService.OperationMetrics operationMetrics) {
//        OperationMetricsInfo info = new OperationMetricsInfo();
//        info.setTotalRetries(operationMetrics.getTotalRetries().sum());
//        info.setSuccessfulRetries(operationMetrics.getSuccessfulRetries().sum());
//        info.setFailedRetries(operationMetrics.getFailedRetries().sum());
//        info.setMaxRetryAttempts(operationMetrics.getMaxRetryAttempts().get());
//        info.setSuccessRate(operationMetrics.getSuccessRate());
//        info.setAverageDuration(operationMetrics.getAverageDuration());
//        info.setLastRetryTime(operationMetrics.getLastRetryTime());
//        info.setLastSuccessTime(operationMetrics.getLastSuccessTime());
//        info.setLastFailureTime(operationMetrics.getLastFailureTime());
//        info.setLastErrorType(operationMetrics.getLastErrorType());
//        return info;
//    }
//
//    /**
//     * 获取重试配置信息
//     */
//    private RetryConfigurationInfo getRetryConfigurationInfo() {
//        RetryConfigurationInfo info = new RetryConfigurationInfo();
//
//        // 默认配置
//        RetryConfig.RetrySettings defaultSettings = retryConfig.getDefaultSettings();
//        info.setDefaultMaxRetries(defaultSettings.getMaxRetries());
//        info.setDefaultBaseDelay(defaultSettings.getBaseDelay());
//        info.setDefaultMaxDelay(defaultSettings.getMaxDelay());
//        info.setDefaultStrategy(defaultSettings.getStrategy().name());
//        info.setDefaultEnableJitter(defaultSettings.isEnableJitter());
//
//        // 操作特定配置
//        Map<String, RetrySettingsInfo> operationSettings = new HashMap<>();
//
//        RetryConfig.RetrySettings uploadSettings = retryConfig.getSettingsForOperation("upload");
//        operationSettings.put("upload", convertRetrySettings(uploadSettings));
//
//        RetryConfig.RetrySettings downloadSettings = retryConfig.getSettingsForOperation("download");
//        operationSettings.put("download", convertRetrySettings(downloadSettings));
//
//        RetryConfig.RetrySettings metadataSettings = retryConfig.getSettingsForOperation("metadata");
//        operationSettings.put("metadata", convertRetrySettings(metadataSettings));
//
//        info.setOperationSettings(operationSettings);
//
//        // 自适应配置
//        RetryConfig.AdaptiveConfig adaptiveConfig = retryConfig.getAdaptive();
//        info.setAdaptiveSuccessRateThreshold(adaptiveConfig.getSuccessRateThreshold());
//        info.setAdaptiveAdjustmentFactor(adaptiveConfig.getAdjustmentFactor());
//        info.setAdaptiveWindowSize(adaptiveConfig.getWindowSize());
//        info.setAdaptiveMinSampleSize(adaptiveConfig.getMinSampleSize());
//
//        // 熔断器配置
//        info.setCircuitBreakerEnabled(retryConfig.isEnableCircuitBreaker());
//        if (retryConfig.isEnableCircuitBreaker()) {
//            RetryConfig.CircuitBreakerConfig cbConfig = retryConfig.getCircuitBreaker();
//            info.setCircuitBreakerFailureRateThreshold(cbConfig.getFailureRateThreshold());
//            info.setCircuitBreakerMinimumNumberOfCalls(cbConfig.getMinimumNumberOfCalls());
//            info.setCircuitBreakerWaitDuration(cbConfig.getWaitDurationInOpenState());
//        }
//
//        return info;
//    }
//
//    /**
//     * 转换重试设置
//     */
//    private RetrySettingsInfo convertRetrySettings(RetryConfig.RetrySettings settings) {
//        RetrySettingsInfo info = new RetrySettingsInfo();
//        info.setMaxRetries(settings.getMaxRetries());
//        info.setBaseDelay(settings.getBaseDelay());
//        info.setMaxDelay(settings.getMaxDelay());
//        info.setStrategy(settings.getStrategy().name());
//        info.setEnableJitter(settings.isEnableJitter());
//        info.setJitterFactor(settings.getJitterFactor());
//        return info;
//    }
//
//    /**
//     * 获取系统信息
//     */
//    private SystemInfo getSystemInfo() {
//        SystemInfo info = new SystemInfo();
//        info.setCurrentTime(LocalDateTime.now());
//        info.setJvmMaxMemory(Runtime.getRuntime().maxMemory());
//        info.setJvmTotalMemory(Runtime.getRuntime().totalMemory());
//        info.setJvmFreeMemory(Runtime.getRuntime().freeMemory());
//        info.setJvmUsedMemory(Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory());
//        info.setAvailableProcessors(Runtime.getRuntime().availableProcessors());
//        return info;
//    }
//
//    // ==================== 数据传输对象 ====================
//
//    @Data
//    public static class StorageRetryMetricsInfo {
//        private GlobalMetricsInfo globalMetrics;
//        private Map<String, OperationMetricsInfo> operationMetrics;
//        private RetryConfigurationInfo retryConfiguration;
//        private SystemInfo systemInfo;
//    }
//
//    @Data
//    public static class GlobalMetricsInfo {
//        private long totalRetries;
//        private long successfulRetries;
//        private long failedRetries;
//        private double globalSuccessRate;
//        private double globalAverageDuration;
//    }
//
//    @Data
//    public static class OperationMetricsInfo {
//        private long totalRetries;
//        private long successfulRetries;
//        private long failedRetries;
//        private long maxRetryAttempts;
//        private double successRate;
//        private double averageDuration;
//        private LocalDateTime lastRetryTime;
//        private LocalDateTime lastSuccessTime;
//        private LocalDateTime lastFailureTime;
//        private String lastErrorType;
//    }
//
//    @Data
//    public static class RetryConfigurationInfo {
//        private int defaultMaxRetries;
//        private long defaultBaseDelay;
//        private long defaultMaxDelay;
//        private String defaultStrategy;
//        private boolean defaultEnableJitter;
//        private Map<String, RetrySettingsInfo> operationSettings;
//        private double adaptiveSuccessRateThreshold;
//        private double adaptiveAdjustmentFactor;
//        private int adaptiveWindowSize;
//        private int adaptiveMinSampleSize;
//        private boolean circuitBreakerEnabled;
//        private double circuitBreakerFailureRateThreshold;
//        private int circuitBreakerMinimumNumberOfCalls;
//        private long circuitBreakerWaitDuration;
//    }
//
//    @Data
//    public static class RetrySettingsInfo {
//        private int maxRetries;
//        private long baseDelay;
//        private long maxDelay;
//        private String strategy;
//        private boolean enableJitter;
//        private double jitterFactor;
//    }
//
//    @Data
//    public static class SystemInfo {
//        private LocalDateTime currentTime;
//        private long jvmMaxMemory;
//        private long jvmTotalMemory;
//        private long jvmFreeMemory;
//        private long jvmUsedMemory;
//        private int availableProcessors;
//    }
//}
