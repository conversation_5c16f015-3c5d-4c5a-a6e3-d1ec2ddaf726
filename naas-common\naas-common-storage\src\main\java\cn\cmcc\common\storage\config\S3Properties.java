package cn.cmcc.common.storage.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 *  S3 存储协议 所有兼容S3协议 分布式存储配置
 * <AUTHOR>
 *
 */

@Component
@ConfigurationProperties(prefix = "s3")
@Data
public class S3Properties {

	/**
	 * 服务的URL
	 */
	private String endpoint;

    /**
	 * Access key
	 */
	private String accessKey;

	/**
	 * Secret key
	 */
	private String secretKey;

    /**
     * 存储区域
     */
    private String region;

    /**
     * 是否https
     */
    private boolean https  = false;

    /**
     * 最大连接数
     */
    private Integer maxConnections = 200;

    /**
     * 连接超时时间(毫秒) - 建立连接的超时时间
     */
    private Integer connectionTimeout = 10000;

    /**
     * 套接字超时时间(毫秒) - 数据传输的超时时间
     */
    private Integer socketTimeout = 50000;

    /**
     * 请求超时时间(毫秒) - 整个请求的超时时间
     */
    private Integer requestTimeout = 300000;

    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;

    /**
     * 连接最大空闲时间(毫秒)
     */
    private Integer maxIdleTime = 60000;

    /**
     * 连接验证间隔(毫秒)
     */
    private Integer validateAfterInactivity = 5000;

    /**
     * 是否启用 GZIP 压缩
     */
    private Boolean useGzip = true;

    /**
     * 是否禁用 Expect: 100-continue
     */
    private Boolean useExpectContinue = false;

	/**
	 * 签名有效期 单位秒
	 */
	private Integer expire;

	/**
	 * 存储桶名称
	 */
	private String bucketName;

}
