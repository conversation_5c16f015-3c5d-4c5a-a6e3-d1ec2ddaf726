package cn.cmcc.common.storage.service;

import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 重试指标统计服务
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
@Slf4j
@Service
public class RetryMetricsService {

    /**
     * 按操作类型统计的指标
     */
    private final ConcurrentHashMap<String, OperationMetrics> operationMetrics = new ConcurrentHashMap<>();

    /**
     * 全局统计指标
     * -- GETTER --
     * 获取全局指标
     *
     * @return 全局指标
     */
    @Getter
    private final GlobalMetrics globalMetrics = new GlobalMetrics();

    /**
     * 记录重试开始
     *
     * @param operationType 操作类型
     * @param attempt       重试次数
     */
    public void recordRetryStart(String operationType, int attempt) {
        OperationMetrics metrics = operationMetrics.computeIfAbsent(operationType, k -> new OperationMetrics());
        metrics.totalRetries.increment();
        metrics.lastRetryTime = LocalDateTime.now();

        globalMetrics.totalRetries.increment();

        if (log.isDebugEnabled()) {
            log.debug("记录重试开始: 操作类型={}, 重试次数={}", operationType, attempt);
        }
    }

    /**
     * 记录重试成功
     *
     * @param operationType 操作类型
     * @param attempt       重试次数
     * @param totalDuration 总耗时（毫秒）
     */
    public void recordRetrySuccess(String operationType, int attempt, long totalDuration) {
        OperationMetrics metrics = operationMetrics.computeIfAbsent(operationType, k -> new OperationMetrics());
        metrics.successfulRetries.increment();
        metrics.totalDuration.add(totalDuration);
        metrics.lastSuccessTime = LocalDateTime.now();

        // 更新最大重试次数
        if (attempt > metrics.maxRetryAttempts.get()) {
            metrics.maxRetryAttempts.set(attempt);
        }

        globalMetrics.successfulRetries.increment();
        globalMetrics.totalDuration.add(totalDuration);

        log.info("重试成功: 操作类型={}, 重试次数={}, 总耗时={}ms", operationType, attempt, totalDuration);
    }

    /**
     * 记录重试失败
     *
     * @param operationType 操作类型
     * @param attempt       重试次数
     * @param errorType     错误类型
     * @param totalDuration 总耗时（毫秒）
     */
    public void recordRetryFailure(String operationType, int attempt, String errorType, long totalDuration) {
        OperationMetrics metrics = operationMetrics.computeIfAbsent(operationType, k -> new OperationMetrics());
        metrics.failedRetries.increment();
        metrics.totalDuration.add(totalDuration);
        metrics.lastFailureTime = LocalDateTime.now();
        metrics.lastErrorType = errorType;

        // 更新最大重试次数
        if (attempt > metrics.maxRetryAttempts.get()) {
            metrics.maxRetryAttempts.set(attempt);
        }

        globalMetrics.failedRetries.increment();
        globalMetrics.totalDuration.add(totalDuration);

        log.warn("重试失败: 操作类型={}, 重试次数={}, 错误类型={}, 总耗时={}ms",
                operationType, attempt, errorType, totalDuration);
    }

    /**
     * 获取操作类型的成功率
     *
     * @param operationType 操作类型
     * @return 成功率（0.0-1.0）
     */
    public double getSuccessRate(String operationType) {
        OperationMetrics metrics = operationMetrics.get(operationType);
        if (metrics == null) {
            return 1.0; // 没有数据时假设成功率为100%
        }

        long total = metrics.successfulRetries.sum() + metrics.failedRetries.sum();
        if (total == 0) {
            return 1.0;
        }

        return (double) metrics.successfulRetries.sum() / total;
    }

    /**
     * 获取操作类型的平均重试延迟
     *
     * @param operationType 操作类型
     * @return 平均延迟（毫秒）
     */
    public double getAverageRetryDelay(String operationType) {
        OperationMetrics metrics = operationMetrics.get(operationType);
        if (metrics == null) {
            return 0.0;
        }

        long totalRetries = metrics.totalRetries.sum();
        if (totalRetries == 0) {
            return 0.0;
        }

        return (double) metrics.totalDuration.sum() / totalRetries;
    }

    /**
     * 获取指定操作类型的指标
     *
     * @param operationType 操作类型
     * @return 操作指标
     */
    public OperationMetrics getOperationMetrics(String operationType) {
        return operationMetrics.get(operationType);
    }

    /**
     * 重置所有指标
     */
    public void resetMetrics() {
        operationMetrics.clear();
        globalMetrics.reset();
        log.info("重试指标已重置");
    }

    /**
     * 操作指标
     */
    @Data
    public static class OperationMetrics {
        /**
         * 总重试次数
         */
        private final LongAdder totalRetries = new LongAdder();

        /**
         * 成功重试次数
         */
        private final LongAdder successfulRetries = new LongAdder();

        /**
         * 失败重试次数
         */
        private final LongAdder failedRetries = new LongAdder();

        /**
         * 总耗时（毫秒）
         */
        private final LongAdder totalDuration = new LongAdder();

        /**
         * 最大重试次数
         */
        private final AtomicLong maxRetryAttempts = new AtomicLong(0);

        /**
         * 最后重试时间
         */
        private volatile LocalDateTime lastRetryTime;

        /**
         * 最后成功时间
         */
        private volatile LocalDateTime lastSuccessTime;

        /**
         * 最后失败时间
         */
        private volatile LocalDateTime lastFailureTime;

        /**
         * 最后错误类型
         */
        private volatile String lastErrorType;

        /**
         * 获取成功率
         */
        public double getSuccessRate() {
            long total = successfulRetries.sum() + failedRetries.sum();
            return total == 0 ? 1.0 : (double) successfulRetries.sum() / total;
        }

        /**
         * 获取平均耗时
         */
        public double getAverageDuration() {
            long total = totalRetries.sum();
            return total == 0 ? 0.0 : (double) totalDuration.sum() / total;
        }
    }

    /**
     * 全局指标
     */
    @Data
    public static class GlobalMetrics {
        private final LongAdder totalRetries = new LongAdder();
        private final LongAdder successfulRetries = new LongAdder();
        private final LongAdder failedRetries = new LongAdder();
        private final LongAdder totalDuration = new LongAdder();

        public void reset() {
            totalRetries.reset();
            successfulRetries.reset();
            failedRetries.reset();
            totalDuration.reset();
        }

        public double getGlobalSuccessRate() {
            long total = successfulRetries.sum() + failedRetries.sum();
            return total == 0 ? 1.0 : (double) successfulRetries.sum() / total;
        }

        public double getGlobalAverageDuration() {
            long total = totalRetries.sum();
            return total == 0 ? 0.0 : (double) totalDuration.sum() / total;
        }
    }
}
