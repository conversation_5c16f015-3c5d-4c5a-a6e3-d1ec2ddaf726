package cn.cmcc.common.storage.config;

/**
 * 重试策略枚举
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
public enum RetryStrategy {
    
    /**
     * 指数退避策略 - 延迟时间呈指数增长
     * 适用于网络波动较大的场景
     */
    EXPONENTIAL_BACKOFF("指数退避"),
    
    /**
     * 线性退避策略 - 延迟时间线性增长
     * 适用于服务负载较高的场景
     */
    LINEAR_BACKOFF("线性退避"),
    
    /**
     * 固定延迟策略 - 每次重试使用相同延迟
     * 适用于已知服务恢复时间的场景
     */
    FIXED_DELAY("固定延迟"),
    
    /**
     * 自适应策略 - 根据历史成功率动态调整
     * 适用于复杂网络环境的智能优化
     */
    ADAPTIVE("自适应");
    
    private final String description;
    
    RetryStrategy(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 计算重试延迟时间
     * 
     * @param attempt 当前重试次数（从1开始）
     * @param baseDelay 基础延迟时间（毫秒）
     * @param maxDelay 最大延迟时间（毫秒）
     * @param successRate 历史成功率（仅自适应策略使用）
     * @return 延迟时间（毫秒）
     */
    public long calculateDelay(int attempt, long baseDelay, long maxDelay, double successRate) {
        switch (this) {
            case EXPONENTIAL_BACKOFF:
                long exponentialDelay = baseDelay * (1L << (attempt - 1));
                return Math.min(exponentialDelay, maxDelay);
                
            case LINEAR_BACKOFF:
                long linearDelay = baseDelay * attempt;
                return Math.min(linearDelay, maxDelay);
                
            case FIXED_DELAY:
                return baseDelay;
                
            case ADAPTIVE:
                // 根据成功率调整延迟：成功率低时延迟更长
                double adjustmentFactor = successRate < 0.5 ? 2.0 : (successRate < 0.8 ? 1.5 : 1.0);
                long adaptiveDelay = (long) (baseDelay * attempt * adjustmentFactor);
                return Math.min(adaptiveDelay, maxDelay);
                
            default:
                return baseDelay;
        }
    }
}
