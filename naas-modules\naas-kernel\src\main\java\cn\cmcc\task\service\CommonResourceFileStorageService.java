package cn.cmcc.task.service;

import cn.cmcc.common.datasource.mapper.CommonCurdMapper;
import cn.cmcc.common.datasource.mapper.CommonMapper;
import cn.cmcc.common.datasource.pojo.bo.TableColumn;
import cn.cmcc.common.datasource.utils.PostgreSQLCopyManager;
import cn.cmcc.common.dict.service.ISysSeqService;
import cn.cmcc.common.enums.CompressTypeEnums;
import cn.cmcc.common.enums.FileTypeEnums;
import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.pojo.LoginUser;
import cn.cmcc.common.pojo.SysConfigBO;
import cn.cmcc.common.utils.DateUtils;
import cn.cmcc.common.utils.MapUtils;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.bean.ObjectUtils;
import cn.cmcc.common.utils.file.CsvUtil;
import cn.cmcc.common.utils.file.FileUtils;
import cn.cmcc.common.utils.file.excel.ExcelToCsv;
import cn.cmcc.common.utils.sql.SqlUtil;
import cn.cmcc.impt.service.ITCommonImportService;
import cn.cmcc.impt.utils.MCsvUtils;
import cn.cmcc.task.pojo.CommonFileStorageConfigInfo;
import cn.cmcc.task.pojo.CommonFileStorageConfigInfo.FileInfo;
import cn.cmcc.task.pojo.CommonFileStorageConfigInfo.HeaderTypeEnum;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;

import static cn.cmcc.kernel.service.impl.CommonCsvImportServiceImpl.buildColumns;

/**
 * 当 PostgreSQL 的连接执行一批 SQL的时候，SQL里面有DML 也有查询。
 * 如果整批 SQL 是一个事务，其中有 SQL 执行报错时,事务的状态会进入 “ABORTED”状态，此后无论执行什么 SQL（正确的、或者查询SQL），都会报错：
 *
 * <AUTHOR>
 * @date 2023年2月23日
 * @version V1.0
 */

@Component
@Slf4j
public class CommonResourceFileStorageService {

	@Autowired
	private CommonMapper commonMapper;
	@Autowired
	private CommonCurdMapper commonCurdMapper;
	@Autowired
	private ISysSeqService sysSeqService;
	@Resource
	protected ITCommonImportService tCommonImportService;
	/**
	 *
	 * 处理文件入库
	 *
	 * @Title: dealFile
	 * @param fileInfo
	 * @param filePath void
	 * @throws Exception
	 * @throws
	 */
	@Transactional(rollbackFor = Exception.class)
	public void dealFile(FileInfo fileInfo, String filePath, SysConfigBO sysConfigBO,String fileAbsolutely) throws Exception {
		try{
			List<String> fileList = CompressTypeEnums.getByValue(fileInfo.getCompressType()).decompress(filePath);
			for (int loop = 0; loop < fileList.size() ; loop ++ ) {
				String filePathName = fileList.get(loop);
				log.info("-->开始同步文件【" + filePathName + "】的数据！表头类型：" + fileInfo.getHeaderType());
				File file = FileUtils.getFile(filePathName);
				if(StringUtils.isEmpty(fileInfo.getHeaderType())) {
					fileInfo.setHeaderType(HeaderTypeEnum.EN.getCode());
				}

				if(FileTypeEnums.EXCEL.getCode().equals(fileInfo.getFileType())) {
					//若是execl，将execl转成csv
					File csvFile = FileUtils.getFile(FilenameUtils.getFullPath(filePathName) + FilenameUtils.getBaseName(filePathName) + ".csv");
					ExcelToCsv.toCsv(file, csvFile, fileInfo.getFileSepartor());
					file = csvFile;
				}

				List<String> headerList;
				//文件表头为英文
				if(HeaderTypeEnum.EN.getCode().equals(fileInfo.getHeaderType())) {
					headerList = CsvUtil.header(file,fileInfo.getFileSepartor().charAt(0),fileInfo.getFileEncode());
					if(headerList==null || headerList.isEmpty()) {
						log.error("文件入库结束，文件表头不能为空，文件名:{}",file.getName());
						continue;
					}
					headerList = headerList.stream().map(s->s.toLowerCase().trim()).collect(Collectors.toList());
					fileInfo.setEnglishFields(String.join(",",headerList));
				}else {
					if(StringUtils.isEmpty(fileInfo.getEnglishFields())) {
						log.error("-->中文表头或无表头需配置英文字段！");
						continue;
					}
					headerList = Arrays.asList(fileInfo.getEnglishFields().split(","));
				}
				log.info("-->同步文件表头【" + headerList.toString() + "】");
				String tableName = fileInfo.getTableName();
				String tableScheme = null;
				if (tableName != null && tableName.contains(".")) {
					String[] array = tableName.split("\\.");
					tableName = array[1];
					tableScheme = array[0];
				}
				tableScheme = StringUtils.isEmpty(tableScheme) ? fileInfo.getSchemeName(): tableScheme;
				if(StringUtils.isEmpty(tableScheme)) {//如果未设置模式，则使用当前链接的模式
					tableScheme = commonMapper.getDBCurrentSchema();
				}
				if(!SqlHelper.retBool(commonMapper.checkTableExist(tableName))) {
					log.info("-->【" + fileInfo.getTableName() + "】表不存在！");
					if (StringUtils.isNotEmpty(fileInfo.getSchemeName())) {
						createTable(fileInfo.getSchemeName() + "." + fileInfo.getTableName(), headerList);
					} else {
						createTable(fileInfo.getTableName(), headerList);
					}
				}
				String code = sysConfigBO.getCode();
				if(code.startsWith("RM_DATA_COLLECTION") || code.startsWith("MR_")){
					if(StringUtils.isEmpty(fileInfo.getBatchNo())){
						fileInfo.setBatchNo("batch_no");
					}
				}
				//增量为false时，表示需要删除旧数据，必须有批次号
				if((fileInfo.isDynamicTableHead() && fileInfo.isIncrement() == false && StringUtils.isEmpty(fileInfo.getBatchNo()))) {
					log.error("错误,不为增量时,批次号不能为空,文件名:{}",fileInfo.getFileName());
					continue;
				}
				if(StringUtils.isNotEmpty(fileInfo.getBatchNo()) && fileInfo.isDynamicTableHead()==false){
					log.warn("批次号{}不为空,请确认是否需要使用动态表头,文件名:{}",fileInfo.getBatchNo(),fileInfo.getFileName());
				}
				String tempTableName = tableName + new Date().getTime() + "_" + Thread.currentThread().getId() ;

				List<TableColumn> tableColumnList = commonMapper.getTableStructure(tableName, tableScheme);
				//创建临时表
				Map<String,String> otherFieldMap = createTempTable(tableColumnList,headerList,fileInfo, tempTableName,sysConfigBO);

				String tableNameCol = tempTableName + "(" + fileInfo.getEnglishFields() + ")";
				//如果原文件是csv格式，为防止对原文件进行更改，复制一份新文件后再进行后续操作
				String absolutePathTmp = copyCsvFile(file);
                if (absolutePathTmp != null) {
                    log.info("原文件: {}, 复制后文件: {}", file.getAbsolutePath(), absolutePathTmp);
                    // 验证复制后的文件编码
                    try (BufferedReader reader = Files.newBufferedReader(Paths.get(absolutePathTmp), StandardCharsets.UTF_8)) {
                        String firstLine = reader.readLine();
                        log.info("复制后文件首行内容验证成功: {}", firstLine != null ? firstLine.substring(0, Math.min(50, firstLine.length())) : "空文件");
                    } catch (Exception e) {
                        log.error("复制后文件UTF-8编码验证失败: {}", e.getMessage());
                    }
                }
				//将csv文件中的内容赋值到临时表,此处应传入csv的绝对路径，而不是xls的路径
				PostgreSQLCopyManager.copyInFromFile(absolutePathTmp == null ? file.getAbsolutePath() : absolutePathTmp,tableNameCol,fileInfo.getFileSepartor(),!HeaderTypeEnum.WU.getCode().equals(fileInfo.getHeaderType()),fileInfo.getFileEncode());
				//复制完数据后，设置自定义主键值、批次号
				fullTempTablePKOrBatchNo(tempTableName,otherFieldMap,fileInfo);
				log.info("-->结束【{}】copy in 操作！",fileInfo.getTableName());
				//从临时表复制到原表-新业务逻辑：一码到底资管数据收集，规划站多维评分，北京性能指标收集*
				if(code.startsWith("RM_DATA_COLLECTION") || code.startsWith("MR_") || fileInfo.isDynamicTableHead()){
					tempTableCopyToSourceTableLogicNew(tableScheme, tableName, tempTableName, headerList, otherFieldMap, sysConfigBO, file,fileInfo);
				}else{
					//从临时表复制到原表-老业务逻辑
					tempTableCopyToSourceTableLogicOld(tempTableName,headerList,otherFieldMap,tableColumnList,fileInfo,loop);
				}
				//导入完成后删除临时表
				int dropResult = commonMapper.update("drop table " + tempTableName);
				log.info("数据结束，已删除临时表,临时表名：{},删除结果:{}",tempTableName,dropResult);
				endHandle(sysConfigBO,tableName,tableScheme);
			}
			log.info("-------------------数据导入结束--------------------");
		} catch (Exception e) {
			log.error("{}",e.getMessage(),e);
			throw new RuntimeException(e);
		}finally {
			File dirFile = new File(fileAbsolutely);
			if(dirFile.exists() == false) return;
			for(File fileIt : dirFile.listFiles()){
				String fileName = fileIt.getName();
				//文件夹和非csv文件直接忽略
				if(fileIt.isDirectory() || fileName.endsWith(".csv") == false) continue;
				if(fileName.startsWith("temp_")){
					log.info("删除临时文件：{}",fileIt.getName());
					fileIt.delete();
				}
			}
		}
	}
	//结尾处理
	private void endHandle(SysConfigBO sysConfigBO,String tableName,String schema){
		//如果导入码以MR——开头，则说明是规划站优先级评分，则更新配置表数据条数
		if(sysConfigBO.getCode().startsWith("MR_")){
			String sql = "UPDATE t_bus_table_config set nettype='${netType}',update_user='${username}', data_num=(\n" +
					"  select count(*) as data_num from  ${tableName}\n" +
					") where table_name='${tableName}' and data_type='basicData'";
			String username = "scheduled";
			LoginUser loginUser = sysConfigBO.getLoginUser();
			if(loginUser != null) username = loginUser.getUserName();
			sql = sql.replaceAll("\\$\\{tableName\\}",schema + "." + tableName).replace("${netType}",DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss")).replace("${username}",username);
			commonMapper.update(sql);
		}
	}
	/**
	 * <AUTHOR>  2024/9/25 下午2:10
	 * @description: 失败数据处理，生成失败数据记录
	 */
	private void failDataHandle(List<Map<String, Object>> failedList,List<String> csvHeaderZn,List<String> csvHeader,String savePath,String fileName){
		String remark = "【注释】：以上记录在导入过程中出现了问题，请检查后重试，确保字符编码和数据类型符合模板规范.";
		List<String> dataList = new ArrayList<>();
		csvHeaderZn.add(0, "错误信息提示");
		dataList.add(String.join(",", csvHeaderZn));
		csvHeader.add(0, "error_info");
//		csvHeader.add(fileName);
		//填充错误信息
		for(Map<String, Object> item : failedList){
			item.put("error_info", StringUtils.EMPTY);
			for(String header : csvHeader) {
				String error = String.valueOf(item.get("error_" + header + "_info"));
				if (StringUtils.isNotEmpty(error) && !"null".equals(error)) {
					item.put("error_info", item.get("error_info") + error);
				}
			}
		}
		//将列表转成字符串
		for(Map<String, Object> item : failedList){
			StringBuilder str = new StringBuilder();
			for(String header : csvHeader) {
				String data = String.valueOf(item.get(header));
				str.append(!"null".equals(data) ? data + "," : ",");
			}
			dataList.add(String.valueOf(str));
		}
		dataList.add(remark);
		MCsvUtils.makeCsvFile(dataList, savePath + File.separator + fileName, CharsetUtil.CHARSET_UTF_8);
		log.warn("生成错误数据文件完毕.");
	}

	/**
	 * <AUTHOR>  2024/9/24 下午5:57
	 * @description: 将临时表数据复制到原表，新逻辑
	 */
	public void tempTableCopyToSourceTableLogicNew(String tableScheme,String tableName,String tempTableName,List<String> headerList,Map<String,String> otherFieldMap,SysConfigBO sysConfigBO,File file,FileInfo fileInfo){
		String tableNameSchema = tableScheme + "." + tableName;//原表模式.表名
		List<Map<String, String>> fieldTypeList = commonCurdMapper.selectColumnsType(tableScheme, tableName);
		boolean sourceTableExistsBatchNo = false;
		if(fileInfo.isIncrement() == false){
			//若目标表中不存在批次号，则添加空列，在执行完毕后删除批次号
			List<String> columnList = fieldTypeList.stream().map(it->it.get("column_name").trim()).collect(Collectors.toList());
			sourceTableExistsBatchNo = columnList.contains(fileInfo.getBatchNo().trim());//原表中是否存在批次号，存在执行后不删除，不存在执行后删除
			if(sourceTableExistsBatchNo == false){//表中不存在批次号列，则添加
				commonMapper.alterAddColumn(tableNameSchema,fileInfo.getBatchNo());//添加批次号列，添加后移除
				fieldTypeList.add(MapUtils.hashMap("column_name",fileInfo.getBatchNo().trim(),"data_type","varchar"));
			}
		}
		List<String> headerListFileCopy = new ArrayList<>(headerList);
		headerListFileCopy.addAll(otherFieldMap.values());
		Map<String,String> headerMap = new HashMap<>();
		//筛选出csv表头列
		List<Map<String, String>> columns = fieldTypeList.stream().filter(s -> headerListFileCopy.contains(s.get("column_name"))).peek(col -> buildColumns(headerMap, col)).collect(Collectors.toList());
		List<Map<String, Object>> failedList = new ArrayList<>();
		Set<String> primaryKeyColumn = Set.of(otherFieldMap.get("pk") == null ? "" : otherFieldMap.get("pk"));

		//将数据从临时表中插入到目标表
		Long successCount = commonCurdMapper.insertOriginalTableFromTextTempTable(tempTableName, tableNameSchema, columns,false);
		int totalCount = commonMapper.update("select count(*) as totalCount from " + tempTableName);
		//成功条数不等于总条数
		if (successCount != totalCount) {
			Map<String,String> primaryKeyMap = columns.stream().filter(it -> it.get("column_name").equals(otherFieldMap.get("pk"))).findFirst().orElse(null);// NOSONAR
			List<Map<String,String>> primaryKeyInfList = primaryKeyMap == null || primaryKeyMap.size() == 0 ? new ArrayList<>(): List.of(primaryKeyMap);
			List<Map<String, Object>> failedDataTypeErrorList = commonCurdMapper.selectInsertFailedDataFromTempTable(tempTableName, tableNameSchema, columns, primaryKeyInfList);
			failedList.addAll(failedDataTypeErrorList);
		}
		String batchNoValue = otherFieldMap.get("batchNoValue");//当前批次号值
		String batchNoFieldName = otherFieldMap.get("batchNoField");//批次号字段
		//如果失败列表为空，则删除所有原数据，此处定时任务调起时，全部成功则删除，若是通过接口调用，全部成功不删除
		if(failedList.isEmpty()){
			//全部导入成功，且是定时任务调起，则删除原表数据
			if(StringUtils.isNotEmpty(batchNoFieldName) && fileInfo.isIncrement()==false){
				int deleteResult = commonCurdMapper.deleteByNotEqColumnValue(tableNameSchema, batchNoFieldName.trim(),batchNoValue.trim());
				log.warn("导入数据全部成功,删除原数据条数：{}",deleteResult);
			}
		}else{
			List<String> csvHeader = new ArrayList<>(headerList);
			if(StringUtils.isNotEmpty(batchNoValue) && StringUtils.isNotEmpty(batchNoFieldName)){
				csvHeader.add(batchNoFieldName);
			}
			CommonFileStorageConfigInfo commonFileStorageConfigInfo = JSON.parseObject(sysConfigBO.getValue(),CommonFileStorageConfigInfo.class, JSONReader.Feature.SupportSmartMatch);
			String fileNameErrorData = file.getName() + DateUtils.getCurrentDateTime("MM-dd_HH:mm:ss") + "_导入失败记录.csv";
			String savePath = commonFileStorageConfigInfo.getFilePath();
			log.warn("导入数据部分失败,总条数:{},导入失败条数:{},开始生成错误数据文件,批次号:{},错误文件存储路径:{}",totalCount,failedList.size(),batchNoValue,savePath+fileNameErrorData);
			failDataHandle(failedList,new ArrayList<>(csvHeader), new ArrayList<>(csvHeader), savePath,fileNameErrorData);
			//如果未导入成功，则删除本次导入的数据
			if(fileInfo.isIncrement()==false && StringUtils.isNotEmpty(batchNoFieldName) && StringUtils.isNotEmpty(batchNoValue)) commonCurdMapper.deleteByMultiField(tableNameSchema,Map.of(batchNoFieldName,batchNoValue));
		}
		//如果原表不存在批次号，则成功后删除
		if(fileInfo.isIncrement()==false && sourceTableExistsBatchNo == false && StringUtils.isNotEmpty(fileInfo.getBatchNo())){
			commonMapper.alterDropColumn(tableNameSchema,fileInfo.getBatchNo());
		}
	}
	/**
	 * <AUTHOR>  2024/9/24 下午3:02
	 * @description: 从临时表复制到源表，原逻辑
	 */
	public void tempTableCopyToSourceTableLogicOld(String tempTableName,List<String> headerList,Map<String,String> otherFieldMap,List<TableColumn> tableColumnList,FileInfo fileInfo,int loop){
		//入库表名带模式
		String updateTableName = ObjectUtils.isNotEmpty(fileInfo.getSchemeName()) ? fileInfo.getSchemeName() + "." + fileInfo.getTableName() : fileInfo.getTableName();
		String sql = "select count(1) cnt from " + tempTableName;
		Map<String, Object> resMap = commonMapper.selectMap(sql);
		if(!resMap.isEmpty() && Long.parseLong(resMap.get("cnt").toString()) > 0) {
			final List<String> headerListTemp = headerList;
			if(!otherFieldMap.isEmpty()){
				headerListTemp.addAll(otherFieldMap.values());
			}
			//csv表头和数据库字段的交集
			String columnNameIntersection = tableColumnList.stream()
					.map(TableColumn::getColumnName)
					.filter(headerListTemp::contains)
					.map(SqlUtil::addSymbol)
					.reduce((x, y) -> x + "," + y ).orElse(StringUtils.EMPTY);
			if(StringUtils.isEmpty(columnNameIntersection)) {
				log.info("csv表头和数据库字段的无交集");
				throw new CustomException("csv表头和数据库字段的无交集");
			}
			String columnNameStr = " (" + columnNameIntersection + ")";

			if(!fileInfo.isIncrement() ) {
				//全量
				//先删除原表
				if(loop == 0) {
					sql = String.format("truncate table %s", updateTableName);
					log.info("开始执行sql:" + sql);
					commonMapper.update(sql);
				}

				sql = String.format("insert into %s%s select  %s from %s", updateTableName, columnNameStr, columnNameIntersection, tempTableName);
				log.info("开始执行sql:" + sql);
				commonMapper.update(sql);
			}else {
				if(StringUtils.isEmpty(fileInfo.getPKey())) {
					sql = String.format("insert into %s%s select %s from %s ", updateTableName, columnNameStr, columnNameIntersection, tempTableName);
					log.info("开始执行sql:" + sql);
					commonMapper.update(sql);
				}else {
					Optional<String> updateColumnNameOptional = tableColumnList.stream()
							.filter(column -> (headerListTemp.contains(column.getColumnName())
									&& !fileInfo.getPKey().equals(column.getColumnName()) )
									|| "update_time".equals(column.getColumnName()))
							.map(column -> {
								if("update_time".equals(column.getColumnName())) {
									return "update_time = CURRENT_TIMESTAMP ";
								}else {
									return "\"" + column.getColumnName()+ "\"=x.\"" + column.getColumnName() + "\"";
								}
							})
							.reduce((x, y) -> x + "," + y);

					if(updateColumnNameOptional.isPresent()) {
						sql = String.format("update %s set %s from %s x where %s.%s=x.%s", updateTableName, updateColumnNameOptional.get(), tempTableName, updateTableName, fileInfo.getPKey(), fileInfo.getPKey());

						log.info("开始执行sql:" + sql);
						commonMapper.update(sql);


						sql =  String.format("insert into %s%s select %s from %s where not EXISTS "
								+ "(select 1 from %s x where x.\"%s\"=%s.\"%s\")", updateTableName, columnNameStr, columnNameIntersection, tempTableName, fileInfo.getTableName(), fileInfo.getPKey(), tempTableName, fileInfo.getPKey());
						log.info("开始执行sql:" + sql);
						commonMapper.update(sql);
					}

				}
			}

		}
	}

	/**
	 * <AUTHOR>  2024/9/24 上午10:46
	 * @description: 若原文件是csv，则复制一个新的，防止转码时更改源文件
	 */
	private String copyCsvFile(File file) throws Exception {
		String absolutePath = file.getAbsolutePath();
		if(absolutePath.endsWith("csv")){
			absolutePath = absolutePath.replace(".csv","");
			String[] array = absolutePath.split("/");
			String fileName = array[array.length-1];
//			absolutePath =  fileName + DateUtils.dateTimeNow("_yyyyMMdd_HHmmss") + ".csv";
			//说明：正常情况下执行完毕会根据临时文件绝对路径删除，少数情况不会删除，而临时文件又太大，故在finally中作了清除，清除条件以“temp_”开头，故而此处不要轻易更改
			absolutePath = "temp_" + fileName + DateUtils.dateTimeNow("_yyyyMMdd_HHmmss") + ".csv";
			array[array.length-1] = absolutePath;
			absolutePath = String.join("/",array);
			File tempFile = new File(absolutePath);// NOSONAR
			if(tempFile.exists()){
				tempFile.delete();// NOSONAR
			}
			FileUtil.copyFile(file,tempFile);
			return absolutePath;
		}
		return null;
	}

    private String copyCsvFile(File file) throws Exception {
        String absolutePath = file.getAbsolutePath();
        if (absolutePath.endsWith("csv")) {
            absolutePath = absolutePath.replace(".csv", "");
            String[] array = absolutePath.split("/");
            String fileName = array[array.length - 1];
            absolutePath = "temp_" + fileName + DateUtils.dateTimeNow("_yyyyMMdd_HHmmss") + ".csv";
            array[array.length - 1] = absolutePath;
            absolutePath = String.join("/", array);
            File tempFile = new File(absolutePath);
            if (tempFile.exists()) {
                tempFile.delete();
            }

            // 修复：先复制，再确保UTF-8编码
            FileUtil.copyFile(file, tempFile);

            // 关键修复：确保复制后的文件是UTF-8编码
            try {
                FileUtil.convertCharset(tempFile, Charset.forName("UTF-8"), Charset.forName("UTF-8"));
            } catch (Exception e) {
                log.warn("文件编码转换失败，尝试重新复制: {}", e.getMessage());
                // 如果转换失败，删除临时文件重新创建
                tempFile.delete();
                Files.copy(file.toPath(), tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            }

            return absolutePath;
        }
        return null;
    }
	/**
	 * <AUTHOR>  2024/9/24 上午9:05
	 * @description: 创建临时表和索引
	 */
	private Map<String,String> createTempTable(List<TableColumn> tableColumnList,List<String> headerList,FileInfo fileInfo,String tempTableName,SysConfigBO sysConfigBO){
		//送过来的主键ID属于表列，且未在表头中，则将其加入header
		List<String> columnList = tableColumnList.stream().map(TableColumn::getColumnName).collect(Collectors.toList());
		List<String> headerListCopy = new ArrayList<>(headerList);
		Map<String,String> otherFieldMap = new HashMap<>(2);
		if(StringUtils.isNotEmpty(fileInfo.getPKey()) && !fileInfo.getEnglishFields().contains(fileInfo.getPKey()) && columnList.contains(fileInfo.getPKey())){
			headerListCopy.add(fileInfo.getPKey());
			otherFieldMap.put("pk",fileInfo.getPKey().trim());
		}
		//送过来的批次号属于该表的一列，且未在表头中，则将其加入表头
		if(StringUtils.isNotEmpty(fileInfo.getBatchNo()) && !fileInfo.getEnglishFields().contains(fileInfo.getBatchNo())){
			headerListCopy.add(fileInfo.getBatchNo());
			otherFieldMap.put("batchNoField",fileInfo.getBatchNo().trim());
		}
		String code = sysConfigBO.getCode();
		//为兼容旧版本，若批次号不为空，且分别使用不同的方法创建临时表
		if(code.startsWith("RM_DATA_COLLECTION") || code.startsWith("MR_") || fileInfo.isDynamicTableHead()){
			//根据文件头创建临时表：一码到底资管数据收集，规划站多维评分
			commonMapper.createTextTempTable(tempTableName,headerListCopy,false);
		}else {
			String tableNameSchema = ObjectUtils.isNotEmpty(fileInfo.getSchemeName()) ? fileInfo.getSchemeName() + "." + fileInfo.getTableName() : fileInfo.getTableName();
			String sqlTmp = SqlUtil.createTableSqlOnExistTable(tempTableName, tableNameSchema, headerListCopy, true);
			//根据原表创建临时表
			commonMapper.update(sqlTmp);
		}
		return otherFieldMap;
	}
	//创建临时表的主键、批次号、索引
	 public void fullTempTablePKOrBatchNo(String tempTableName,Map<String,String> otherFieldMap,FileInfo fileInfo){
		 //创建临时表完成后，设主键
		 if(otherFieldMap.get("pk") != null){
			 commonMapper.update(new SQL().UPDATE(tempTableName).SET(String.format("%s = uuid_generate_v4(32)", fileInfo.getPKey())).WHERE("1 = 1").toString());
		 }
		 String importCode = null;
		 //创建临时表完成后，设批次号
		 if(otherFieldMap.get("batchNoField") != null){
			 String baseKey = String.format("BC_%s",DateUtils.getCurrentDateTime("yyyyMMdd_HHmmss"));
			 commonMapper.update(new SQL().UPDATE(tempTableName).SET(String.format("%s = '%s'",fileInfo.getBatchNo().trim(),baseKey)).WHERE("1 = 1").toString());
			 otherFieldMap.put("batchNoValue",baseKey);
		 }
		 if(StringUtils.isNotEmpty(fileInfo.getPKey())) {
			 //创建临时表索引
			 commonMapper.update(SqlUtil.createTableIndex(tempTableName, fileInfo.getPKey()));
		 }
	 }

	/**
	 *
	 * 创建表
	 *
	 * @Title: createTable
	 * @param tableName
	 * @param headerList void
	 * @throws
	 */
	private void createTable(String tableName, List<String> headerList) {
		List<String> newList = new ArrayList<>(headerList);
		if(!newList.contains("insert_time")) newList.add("insert_time");
		if(!newList.contains("update_time")) newList.add("update_time");
    	String createSql = "CREATE TABLE " + tableName+ "(";
        Optional<String> headerReduce = newList.stream().distinct()
                .map(item->{
                	if("insert_time".equals(item) || "update_time".equals(item)) {
                		return "\"" + item + "\"  timestamp(6)  DEFAULT CURRENT_TIMESTAMP ";
                	}else {
                		return "\"" + item + "\"  varchar(4096)";
                	}
                }).reduce((item, value) -> item + ",  " + value);

		if (headerReduce.isPresent()) {
        	createSql = createSql + headerReduce.get()+")";
            log.info("开始执行sql:" + createSql);
            commonMapper.update(createSql);
        }
    }
}
