# 集成测试配置
# 需要真实的S3服务（MinIO或AWS S3）

s3:
  # 真实S3服务配置（需要根据实际环境修改）
  endpoint: ${INTEGRATION_S3_ENDPOINT:http://10.18.18.37:9010/}
  accessKey: ${INTEGRATION_S3_ACCESS_KEY:cmcc_minio}
  secretKey: ${INTEGRATION_S3_SECRET_KEY:cmcc_minio}
  region: ${INTEGRATION_S3_REGION:us-east-1}
  https: ${INTEGRATION_S3_HTTPS:false}
  maxConnections: 50
  bucketName: ${INTEGRATION_S3_BUCKET:integration-test-bucket}

  # 连接超时配置（集成测试使用较短超时）
  connectionTimeout: 5000
  socketTimeout: 10000
  requestTimeout: 30000

  # 连接池配置
  maxIdleTime: 30000
  validateAfterInactivity: 2000

  # HTTP配置
  useGzip: true
  useExpectContinue: false

  # 集成测试重试配置
  retry:
    # 默认重试配置
    default:
      maxRetries: 2
      baseDelay: 500
      maxDelay: 5000
      strategy: EXPONENTIAL_BACKOFF
      enableJitter: true
      jitterFactor: 0.1

    # 按操作类型的重试配置
    operationSpecific:
      upload:
        maxRetries: 3
        baseDelay: 1000
        maxDelay: 10000
        strategy: EXPONENTIAL_BACKOFF
        enableJitter: true
        jitterFactor: 0.15

      download:
        maxRetries: 2
        baseDelay: 500
        maxDelay: 5000
        strategy: LINEAR_BACKOFF
        enableJitter: true
        jitterFactor: 0.1

      metadata:
        maxRetries: 1
        baseDelay: 200
        maxDelay: 2000
        strategy: FIXED_DELAY
        fixedDelay: 1000
        enableJitter: false

    # 按错误类型的重试配置
    errorSpecific:
      connection_timeout:
        maxRetries: 3
        baseDelay: 1000
        strategy: EXPONENTIAL_BACKOFF

      read_timeout:
        maxRetries: 2
        baseDelay: 800
        strategy: LINEAR_BACKOFF

      socket_timeout:
        maxRetries: 2
        baseDelay: 600
        strategy: EXPONENTIAL_BACKOFF

      connection_refused:
        maxRetries: 4
        baseDelay: 2000
        strategy: EXPONENTIAL_BACKOFF

      network_unreachable:
        maxRetries: 5
        baseDelay: 3000
        strategy: FIXED_DELAY
        fixedDelay: 3000

    # 自适应配置
    adaptive:
      successRateThreshold: 0.7
      adjustmentFactor: 1.3
      windowSize: 50
      minSampleSize: 5

    # 熔断器配置（集成测试中禁用）
    enableCircuitBreaker: false

# 日志配置
logging:
  level:
    cn.cmcc.common.storage: DEBUG
    cn.cmcc.common.storage.service.RetryMetricsService: INFO
    cn.cmcc.common.storage.service.EnhancedRetryExecutor: DEBUG
    com.amazonaws: WARN
    org.apache.http: WARN
    root: INFO

  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 测试配置
test:
  # 集成测试超时配置
  timeout:
    upload: 30000      # 上传超时30秒
    download: 20000    # 下载超时20秒
    concurrent: 60000  # 并发测试超时60秒

  # 测试数据配置
  data:
    smallFileSize: 1024      # 1KB
    mediumFileSize: 102400   # 100KB
    largeFileSize: 1048576   # 1MB

  # 并发测试配置
  concurrency:
    maxThreads: 20
    defaultConcurrency: 10
    highConcurrency: 50

# Spring Boot测试配置
spring:
  test:
    database:
      replace: none

  # 禁用不需要的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# 管理端点配置（用于集成测试监控）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,storage-retry-metrics
  endpoint:
    health:
      show-details: always
    storage-retry-metrics:
      enabled: true

  # 指标配置
  metrics:
    export:
      simple:
        enabled: true
    enable:
      jvm: true
      system: true
      process: true

# 集成测试环境变量说明
# 运行集成测试前需要设置以下环境变量：
#
# INTEGRATION_TEST_ENABLED=true                    # 启用集成测试
# INTEGRATION_S3_ENDPOINT=http://localhost:9000    # S3服务端点
# INTEGRATION_S3_ACCESS_KEY=minioadmin             # S3访问密钥
# INTEGRATION_S3_SECRET_KEY=minioadmin             # S3秘密密钥
# INTEGRATION_S3_BUCKET=integration-test-bucket    # S3存储桶名称
# INTEGRATION_S3_REGION=us-east-1                  # S3区域
# INTEGRATION_S3_HTTPS=false                       # 是否使用HTTPS
#
# MinIO服务启动命令示例：
# docker run -p 9000:9000 -p 9001:9001 \
#   -e "MINIO_ROOT_USER=minioadmin" \
#   -e "MINIO_ROOT_PASSWORD=minioadmin" \
#   minio/minio server /data --console-address ":9001"
