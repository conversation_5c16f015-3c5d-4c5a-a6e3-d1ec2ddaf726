package cn.cmcc.common.storage.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 重试配置类 - 支持分层和动态配置
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "s3.retry")
public class RetryConfig {
    
    /**
     * 默认重试配置
     */
    private RetrySettings defaultSettings = new RetrySettings();
    
    /**
     * 按操作类型的重试配置
     */
    private Map<String, RetrySettings> operationSpecific = new HashMap<>();
    
    /**
     * 按错误类型的重试配置
     */
    private Map<String, RetrySettings> errorSpecific = new HashMap<>();
    
    /**
     * 自适应配置
     */
    private AdaptiveConfig adaptive = new AdaptiveConfig();
    
    /**
     * 是否启用熔断器
     */
    private boolean enableCircuitBreaker = false;
    
    /**
     * 熔断器配置
     */
    private CircuitBreakerConfig circuitBreaker = new CircuitBreakerConfig();
    
    /**
     * 获取指定操作类型的重试配置
     * 
     * @param operationType 操作类型（upload, download, metadata等）
     * @return 重试配置
     */
    public RetrySettings getSettingsForOperation(String operationType) {
        return operationSpecific.getOrDefault(operationType, defaultSettings);
    }
    
    /**
     * 获取指定错误类型的重试配置
     * 
     * @param errorType 错误类型
     * @return 重试配置，如果没有特定配置则返回null
     */
    public RetrySettings getSettingsForError(String errorType) {
        return errorSpecific.get(errorType);
    }
    
    /**
     * 重试设置
     */
    @Data
    public static class RetrySettings {
        /**
         * 最大重试次数
         */
        private int maxRetries = 3;
        
        /**
         * 基础延迟时间（毫秒）
         */
        private long baseDelay = 1000;
        
        /**
         * 最大延迟时间（毫秒）
         */
        private long maxDelay = 30000;
        
        /**
         * 重试策略
         */
        private RetryStrategy strategy = RetryStrategy.EXPONENTIAL_BACKOFF;
        
        /**
         * 固定延迟时间（仅当策略为FIXED_DELAY时使用）
         */
        private long fixedDelay = 5000;
        
        /**
         * 是否启用抖动（在延迟时间基础上添加随机性）
         */
        private boolean enableJitter = true;
        
        /**
         * 抖动因子（0.0-1.0）
         */
        private double jitterFactor = 0.1;
    }
    
    /**
     * 自适应配置
     */
    @Data
    public static class AdaptiveConfig {
        /**
         * 成功率阈值
         */
        private double successRateThreshold = 0.8;
        
        /**
         * 调整因子
         */
        private double adjustmentFactor = 1.5;
        
        /**
         * 统计窗口大小（用于计算成功率）
         */
        private int windowSize = 100;
        
        /**
         * 最小样本数（达到此数量后才开始自适应调整）
         */
        private int minSampleSize = 10;
    }
    
    /**
     * 熔断器配置
     */
    @Data
    public static class CircuitBreakerConfig {
        /**
         * 失败率阈值（触发熔断）
         */
        private double failureRateThreshold = 0.5;
        
        /**
         * 最小请求数（达到此数量后才考虑熔断）
         */
        private int minimumNumberOfCalls = 10;
        
        /**
         * 熔断持续时间（毫秒）
         */
        private long waitDurationInOpenState = 60000;
        
        /**
         * 半开状态下的测试请求数
         */
        private int permittedNumberOfCallsInHalfOpenState = 3;
        
        /**
         * 滑动窗口大小
         */
        private int slidingWindowSize = 100;
    }
}
