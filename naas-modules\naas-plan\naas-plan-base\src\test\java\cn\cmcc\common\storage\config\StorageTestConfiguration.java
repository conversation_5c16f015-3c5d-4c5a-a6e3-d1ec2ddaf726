package cn.cmcc.common.storage.config;

import cn.cmcc.common.storage.service.EnhancedRetryExecutor;
import cn.cmcc.common.storage.service.RetryMetricsService;
import cn.cmcc.common.storage.service.impl.StorageService;
import com.amazonaws.services.s3.AmazonS3;
import lombok.extern.slf4j.Slf4j;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * 存储服务测试配置类
 * 为测试环境提供必要的Bean配置
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
@Slf4j
@TestConfiguration
@Profile({"test", "integration-test"})
public class StorageTestConfiguration {

    /**
     * 提供测试用的S3Properties配置
     */
    @Bean
    @Primary
    public S3Properties testS3Properties() {
        S3Properties properties = new S3Properties();
        properties.setEndpoint("http://10.18.18.37:9010");
        properties.setAccessKey("cmcc_minio");
        properties.setSecretKey("cmcc_minio");
        properties.setRegion("us-east-1");
        properties.setHttps(false);
        properties.setMaxConnections(50);
        properties.setBucketName("integration-test-bucket");
        properties.setConnectionTimeout(5000);
        properties.setSocketTimeout(10000);
        properties.setRequestTimeout(30000);
        properties.setMaxRetries(3);
        properties.setMaxIdleTime(30000);
        properties.setValidateAfterInactivity(2000);
        properties.setUseGzip(true);
        properties.setUseExpectContinue(false);
        return properties;
    }

    /**
     * 提供测试用的重试配置
     */
    @Bean
    @Primary
    public RetryConfig testRetryConfig() {
        RetryConfig config = new RetryConfig();

        // 默认配置
        RetryConfig.RetrySettings defaultSettings = new RetryConfig.RetrySettings();
        defaultSettings.setMaxRetries(3);
        defaultSettings.setBaseDelay(1000);
        defaultSettings.setMaxDelay(30000);
        defaultSettings.setStrategy(RetryStrategy.EXPONENTIAL_BACKOFF);
        defaultSettings.setEnableJitter(true);
        defaultSettings.setJitterFactor(0.1);
        config.setDefaultSettings(defaultSettings);

        // 上传配置
        RetryConfig.RetrySettings uploadSettings = new RetryConfig.RetrySettings();
        uploadSettings.setMaxRetries(5);
        uploadSettings.setBaseDelay(2000);
        uploadSettings.setMaxDelay(60000);
        uploadSettings.setStrategy(RetryStrategy.EXPONENTIAL_BACKOFF);
        uploadSettings.setEnableJitter(true);
        uploadSettings.setJitterFactor(0.15);
        config.getOperationSpecific().put("upload", uploadSettings);

        // 下载配置
        RetryConfig.RetrySettings downloadSettings = new RetryConfig.RetrySettings();
        downloadSettings.setMaxRetries(3);
        downloadSettings.setBaseDelay(1000);
        downloadSettings.setMaxDelay(30000);
        downloadSettings.setStrategy(RetryStrategy.LINEAR_BACKOFF);
        downloadSettings.setEnableJitter(true);
        downloadSettings.setJitterFactor(0.1);
        config.getOperationSpecific().put("download", downloadSettings);

        // 元数据配置
        RetryConfig.RetrySettings metadataSettings = new RetryConfig.RetrySettings();
        metadataSettings.setMaxRetries(2);
        metadataSettings.setBaseDelay(500);
        metadataSettings.setMaxDelay(10000);
        metadataSettings.setStrategy(RetryStrategy.FIXED_DELAY);
        metadataSettings.setFixedDelay(1000);
        metadataSettings.setEnableJitter(false);
        config.getOperationSpecific().put("metadata", metadataSettings);

        // 错误特定配置
        RetryConfig.RetrySettings timeoutSettings = new RetryConfig.RetrySettings();
        timeoutSettings.setMaxRetries(5);
        timeoutSettings.setBaseDelay(2000);
        timeoutSettings.setStrategy(RetryStrategy.EXPONENTIAL_BACKOFF);
        config.getErrorSpecific().put("connection_timeout", timeoutSettings);

        // 自适应配置
        RetryConfig.AdaptiveConfig adaptiveConfig = new RetryConfig.AdaptiveConfig();
        adaptiveConfig.setSuccessRateThreshold(0.8);
        adaptiveConfig.setAdjustmentFactor(1.5);
        adaptiveConfig.setWindowSize(100);
        adaptiveConfig.setMinSampleSize(10);
        config.setAdaptive(adaptiveConfig);

        // 熔断器配置
        config.setEnableCircuitBreaker(false); // 测试环境禁用熔断器

        return config;
    }

    /**
     * 提供重试指标服务
     */
    @Bean
    @Primary
    public RetryMetricsService testRetryMetricsService() {
        return new RetryMetricsService();
    }

    /**
     * 提供增强重试执行器
     */
    @Bean
    @Primary
    public EnhancedRetryExecutor testEnhancedRetryExecutor(
            RetryConfig retryConfig,
            RetryMetricsService metricsService) {
        return new EnhancedRetryExecutor();
    }

    /**
     * 为单元测试提供Mock的AmazonS3客户端
     * 仅在非集成测试环境中使用
     */
    @Bean
    @Primary
    @Profile("test")
    public AmazonS3 mockAmazonS3() {
        AmazonS3 mockS3 = Mockito.mock(AmazonS3.class);

        // 设置基本的Mock行为，避免初始化时的真实连接
        Mockito.when(mockS3.doesBucketExistV2(Mockito.anyString())).thenReturn(true);
        // createBucket返回Bucket对象，不是void方法
        Mockito.when(mockS3.createBucket(Mockito.anyString())).thenReturn(new com.amazonaws.services.s3.model.Bucket("test-bucket"));

        return mockS3;
    }

    /**
     * 为测试提供专门的StorageService，跳过初始化检查
     */
    @Bean
    @Primary
    @Profile("test")
    public cn.cmcc.common.storage.service.impl.StorageService testStorageService(
            S3Properties s3Properties,
            EnhancedRetryExecutor retryExecutor,
            AmazonS3 amazonS3) {

        // 创建StorageService实例但跳过@PostConstruct初始化
        cn.cmcc.common.storage.service.impl.StorageService storageService =
            new cn.cmcc.common.storage.service.impl.StorageService() {
                // 重写init方法，在测试环境中跳过真实的S3连接检查
                @Override
                public void init() {
                    // 在测试环境中不执行真实的初始化逻辑
                    log.info("测试环境：跳过StorageService真实初始化");
                }
            };

        // 手动设置依赖
        org.springframework.test.util.ReflectionTestUtils.setField(storageService, "s3Properties", s3Properties);
        org.springframework.test.util.ReflectionTestUtils.setField(storageService, "retryExecutor", retryExecutor);
        org.springframework.test.util.ReflectionTestUtils.setField(storageService, "client", amazonS3);

        return storageService;
    }

    /**
     * 为集成测试提供真实的StorageService
     * 仅在集成测试环境中使用
     */
    @Bean
    @Primary
    @Profile("integration-test")
    public cn.cmcc.common.storage.service.impl.StorageService integrationTestStorageService(
            S3Properties s3Properties,
            EnhancedRetryExecutor retryExecutor) {

        // 集成测试使用真实的StorageService，但需要真实的S3服务
        cn.cmcc.common.storage.service.impl.StorageService storageService =
            new cn.cmcc.common.storage.service.impl.StorageService();

        // 手动设置依赖
        org.springframework.test.util.ReflectionTestUtils.setField(storageService, "s3Properties", s3Properties);
        org.springframework.test.util.ReflectionTestUtils.setField(storageService, "retryExecutor", retryExecutor);

        return storageService;
    }
}
