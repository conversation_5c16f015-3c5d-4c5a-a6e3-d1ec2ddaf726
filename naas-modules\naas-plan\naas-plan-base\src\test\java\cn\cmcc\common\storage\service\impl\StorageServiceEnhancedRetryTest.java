package cn.cmcc.common.storage.service.impl;

import cn.cmcc.common.storage.config.RetryConfig;
import cn.cmcc.common.storage.config.RetryStrategy;
import cn.cmcc.common.storage.config.S3Properties;
import cn.cmcc.common.storage.service.EnhancedRetryExecutor;
import cn.cmcc.common.storage.service.RetryMetricsService;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StorageService 增强重试功能测试类
 * 测试新的重试策略、指标统计、配置灵活性等功能
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
@SpringBootTest(classes = cn.cmcc.common.storage.StorageTestApplication.class)
@ActiveProfiles({"storage-enhanced"})
@TestPropertySource(locations = "classpath:application-test.yml")
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class StorageServiceEnhancedRetryTest {

    @Mock
    private AmazonS3 mockS3Client;

    @Mock
    private S3Properties mockS3Properties;

    @Mock
    private EnhancedRetryExecutor mockRetryExecutor;

    @Mock
    private RetryMetricsService mockMetricsService;

    @Mock
    private RetryConfig mockRetryConfig;

    @Mock
    private HttpServletResponse mockResponse;

    @Mock
    private S3Object mockS3Object;

    @Mock
    private S3ObjectInputStream mockInputStream;

    @Mock
    private ObjectMetadata mockObjectMetadata;

    @Mock
    private MultipartFile mockMultipartFile;

    @InjectMocks
    private StorageService storageService;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        when(mockS3Properties.getMaxRetries()).thenReturn(3);
        when(mockS3Properties.getBucketName()).thenReturn("integration-test-bucket");
        when(mockS3Properties.getConnectionTimeout()).thenReturn(10000);
        when(mockS3Properties.getSocketTimeout()).thenReturn(50000);
        when(mockS3Properties.getRequestTimeout()).thenReturn(300000);

        // 设置重试配置
        RetryConfig.RetrySettings defaultSettings = new RetryConfig.RetrySettings();
        defaultSettings.setMaxRetries(3);
        defaultSettings.setBaseDelay(1000);
        defaultSettings.setMaxDelay(30000);
        defaultSettings.setStrategy(RetryStrategy.EXPONENTIAL_BACKOFF);
        when(mockRetryConfig.getDefaultSettings()).thenReturn(defaultSettings);
        when(mockRetryConfig.getSettingsForOperation(anyString())).thenReturn(defaultSettings);

        // 使用反射设置私有字段
        ReflectionTestUtils.setField(storageService, "client", mockS3Client);
    }

    // ==================== 增强重试策略测试 ====================

    @Test
    void testEnhancedRetryExecutor_ExponentialBackoff() throws Exception {
        // 准备测试数据
        byte[] testData = "test file content".getBytes();
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("test.txt");

        PutObjectResult mockResult = new PutObjectResult();
        mockResult.setContentMd5("test-md5");

        // 模拟增强重试执行器的行为
        when(mockRetryExecutor.executeWithRetry(eq("upload"), any(), anyString()))
                .thenReturn(mockResult);

        // 执行测试
        String result = storageService.upload(mockMultipartFile);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("test.txt"));
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("upload"), any(), anyString());
    }

    @Test
    void testEnhancedRetryExecutor_LinearBackoff() throws Exception {
        // 准备测试数据
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockResponse.getOutputStream()).thenReturn(mock(ServletOutputStream.class));
        when(mockResponse.isCommitted()).thenReturn(false);

        // 模拟线性退避策略
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);

        // 执行测试
        storageService.downLoad("test-file.txt", mockResponse);

        // 验证结果
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    @Test
    void testEnhancedRetryExecutor_FixedDelay() throws Exception {
        // 准备测试数据
        when(mockS3Object.getObjectMetadata()).thenReturn(mockObjectMetadata);

        // 模拟固定延迟策略
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);

        // 执行测试
        ObjectMetadata result = storageService.getObjectMetadata("/integration-test-bucket/test-file.txt");

        // 验证结果
        assertNotNull(result);
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    @Test
    void testEnhancedRetryExecutor_AdaptiveStrategy() throws Exception {
        // 准备测试数据
        when(mockObjectMetadata.getContentLength()).thenReturn(1024L);
        when(mockS3Object.getObjectMetadata()).thenReturn(mockObjectMetadata);
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockInputStream.read(any(byte[].class))).thenReturn(100);

        // 模拟自适应策略
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);

        // 执行测试
        boolean result = storageService.checkFileDownloadable("test-file.txt");

        // 验证结果
        assertTrue(result);
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    // ==================== 重试策略枚举测试 ====================

    @Test
    void testRetryStrategy_ExponentialBackoff() {
        RetryStrategy strategy = RetryStrategy.EXPONENTIAL_BACKOFF;

        // 测试指数退避计算
        long delay1 = strategy.calculateDelay(1, 1000, 30000, 0.8);
        long delay2 = strategy.calculateDelay(2, 1000, 30000, 0.8);
        long delay3 = strategy.calculateDelay(3, 1000, 30000, 0.8);

        assertEquals(1000, delay1);
        assertEquals(2000, delay2);
        assertEquals(4000, delay3);

        // 测试最大延迟限制
        long delayMax = strategy.calculateDelay(10, 1000, 5000, 0.8);
        assertEquals(5000, delayMax);
    }

    @Test
    void testRetryStrategy_LinearBackoff() {
        RetryStrategy strategy = RetryStrategy.LINEAR_BACKOFF;

        // 测试线性退避计算
        long delay1 = strategy.calculateDelay(1, 1000, 30000, 0.8);
        long delay2 = strategy.calculateDelay(2, 1000, 30000, 0.8);
        long delay3 = strategy.calculateDelay(3, 1000, 30000, 0.8);

        assertEquals(1000, delay1);
        assertEquals(2000, delay2);
        assertEquals(3000, delay3);
    }

    @Test
    void testRetryStrategy_FixedDelay() {
        RetryStrategy strategy = RetryStrategy.FIXED_DELAY;

        // 测试固定延迟计算
        long delay1 = strategy.calculateDelay(1, 1000, 30000, 0.8);
        long delay2 = strategy.calculateDelay(2, 1000, 30000, 0.8);
        long delay3 = strategy.calculateDelay(3, 1000, 30000, 0.8);

        assertEquals(1000, delay1);
        assertEquals(1000, delay2);
        assertEquals(1000, delay3);
    }

    @Test
    void testRetryStrategy_Adaptive() {
        RetryStrategy strategy = RetryStrategy.ADAPTIVE;

        // 测试自适应延迟计算
        // 高成功率场景
        long delayHighSuccess = strategy.calculateDelay(2, 1000, 30000, 0.9);
        assertEquals(2000, delayHighSuccess); // 2 * 1000 * 1.0

        // 中等成功率场景
        long delayMediumSuccess = strategy.calculateDelay(2, 1000, 30000, 0.7);
        assertEquals(3000, delayMediumSuccess); // 2 * 1000 * 1.5

        // 低成功率场景
        long delayLowSuccess = strategy.calculateDelay(2, 1000, 30000, 0.3);
        assertEquals(4000, delayLowSuccess); // 2 * 1000 * 2.0
    }

    // ==================== 配置灵活性测试 ====================

    @Test
    void testRetryConfig_OperationSpecific() {
        // 测试按操作类型的配置
        RetryConfig.RetrySettings uploadSettings = new RetryConfig.RetrySettings();
        uploadSettings.setMaxRetries(5);
        uploadSettings.setBaseDelay(2000);
        uploadSettings.setStrategy(RetryStrategy.EXPONENTIAL_BACKOFF);

        RetryConfig.RetrySettings downloadSettings = new RetryConfig.RetrySettings();
        downloadSettings.setMaxRetries(3);
        downloadSettings.setBaseDelay(1000);
        downloadSettings.setStrategy(RetryStrategy.LINEAR_BACKOFF);

        when(mockRetryConfig.getSettingsForOperation("upload")).thenReturn(uploadSettings);
        when(mockRetryConfig.getSettingsForOperation("download")).thenReturn(downloadSettings);

        // 验证配置
        RetryConfig.RetrySettings actualUploadSettings = mockRetryConfig.getSettingsForOperation("upload");
        assertEquals(5, actualUploadSettings.getMaxRetries());
        assertEquals(2000, actualUploadSettings.getBaseDelay());
        assertEquals(RetryStrategy.EXPONENTIAL_BACKOFF, actualUploadSettings.getStrategy());

        RetryConfig.RetrySettings actualDownloadSettings = mockRetryConfig.getSettingsForOperation("download");
        assertEquals(3, actualDownloadSettings.getMaxRetries());
        assertEquals(1000, actualDownloadSettings.getBaseDelay());
        assertEquals(RetryStrategy.LINEAR_BACKOFF, actualDownloadSettings.getStrategy());
    }

    @Test
    void testRetryConfig_ErrorSpecific() {
        // 测试按错误类型的配置
        RetryConfig.RetrySettings timeoutSettings = new RetryConfig.RetrySettings();
        timeoutSettings.setMaxRetries(5);
        timeoutSettings.setBaseDelay(2000);
        timeoutSettings.setStrategy(RetryStrategy.EXPONENTIAL_BACKOFF);

        when(mockRetryConfig.getSettingsForError("connection_timeout")).thenReturn(timeoutSettings);
        when(mockRetryConfig.getSettingsForError("unknown_error")).thenReturn(null);

        // 验证配置
        RetryConfig.RetrySettings actualTimeoutSettings = mockRetryConfig.getSettingsForError("connection_timeout");
        assertNotNull(actualTimeoutSettings);
        assertEquals(5, actualTimeoutSettings.getMaxRetries());

        RetryConfig.RetrySettings actualUnknownSettings = mockRetryConfig.getSettingsForError("unknown_error");
        assertNull(actualUnknownSettings);
    }

    @Test
    void testRetryConfig_AdaptiveConfig() {
        // 测试自适应配置
        RetryConfig.AdaptiveConfig adaptiveConfig = new RetryConfig.AdaptiveConfig();
        adaptiveConfig.setSuccessRateThreshold(0.8);
        adaptiveConfig.setAdjustmentFactor(1.5);
        adaptiveConfig.setWindowSize(100);
        adaptiveConfig.setMinSampleSize(10);

        when(mockRetryConfig.getAdaptive()).thenReturn(adaptiveConfig);

        // 验证配置
        RetryConfig.AdaptiveConfig actualConfig = mockRetryConfig.getAdaptive();
        assertEquals(0.8, actualConfig.getSuccessRateThreshold());
        assertEquals(1.5, actualConfig.getAdjustmentFactor());
        assertEquals(100, actualConfig.getWindowSize());
        assertEquals(10, actualConfig.getMinSampleSize());
    }

    @Test
    void testRetryConfig_CircuitBreakerConfig() {
        // 测试熔断器配置
        RetryConfig.CircuitBreakerConfig cbConfig = new RetryConfig.CircuitBreakerConfig();
        cbConfig.setFailureRateThreshold(0.5);
        cbConfig.setMinimumNumberOfCalls(10);
        cbConfig.setWaitDurationInOpenState(60000);
        cbConfig.setPermittedNumberOfCallsInHalfOpenState(3);
        cbConfig.setSlidingWindowSize(100);

        when(mockRetryConfig.isEnableCircuitBreaker()).thenReturn(true);
        when(mockRetryConfig.getCircuitBreaker()).thenReturn(cbConfig);

        // 验证配置
        assertTrue(mockRetryConfig.isEnableCircuitBreaker());
        RetryConfig.CircuitBreakerConfig actualConfig = mockRetryConfig.getCircuitBreaker();
        assertEquals(0.5, actualConfig.getFailureRateThreshold());
        assertEquals(10, actualConfig.getMinimumNumberOfCalls());
        assertEquals(60000, actualConfig.getWaitDurationInOpenState());
        assertEquals(3, actualConfig.getPermittedNumberOfCallsInHalfOpenState());
        assertEquals(100, actualConfig.getSlidingWindowSize());
    }

    // ==================== 指标统计测试 ====================

    @Test
    void testRetryMetrics_Recording() {
        // 测试指标记录功能
        doNothing().when(mockMetricsService).recordRetryStart(anyString(), anyInt());
        doNothing().when(mockMetricsService).recordRetrySuccess(anyString(), anyInt(), anyLong());
        doNothing().when(mockMetricsService).recordRetryFailure(anyString(), anyInt(), anyString(), anyLong());

        // 模拟指标记录
        mockMetricsService.recordRetryStart("upload", 1);
        mockMetricsService.recordRetrySuccess("upload", 2, 1500);
        mockMetricsService.recordRetryFailure("download", 3, "connection_timeout", 2000);

        // 验证调用
        verify(mockMetricsService, times(1)).recordRetryStart("upload", 1);
        verify(mockMetricsService, times(1)).recordRetrySuccess("upload", 2, 1500);
        verify(mockMetricsService, times(1)).recordRetryFailure("download", 3, "connection_timeout", 2000);
    }

    @Test
    void testRetryMetrics_SuccessRate() {
        // 测试成功率计算
        when(mockMetricsService.getSuccessRate("upload")).thenReturn(0.85);
        when(mockMetricsService.getSuccessRate("download")).thenReturn(0.92);
        when(mockMetricsService.getSuccessRate("metadata")).thenReturn(1.0);

        // 验证成功率
        assertEquals(0.85, mockMetricsService.getSuccessRate("upload"));
        assertEquals(0.92, mockMetricsService.getSuccessRate("download"));
        assertEquals(1.0, mockMetricsService.getSuccessRate("metadata"));
    }

    @Test
    void testRetryMetrics_AverageDelay() {
        // 测试平均延迟计算
        when(mockMetricsService.getAverageRetryDelay("upload")).thenReturn(1250.0);
        when(mockMetricsService.getAverageRetryDelay("download")).thenReturn(800.0);

        // 验证平均延迟
        assertEquals(1250.0, mockMetricsService.getAverageRetryDelay("upload"));
        assertEquals(800.0, mockMetricsService.getAverageRetryDelay("download"));
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的输出流
     */
    private OutputStream createTestOutputStream() {
        return new ByteArrayOutputStream();
    }

    /**
     * 创建测试用的文件内容
     */
    private byte[] createTestFileContent() {
        return "This is enhanced retry test file content".getBytes();
    }
}
