package cn.cmcc.common.storage.service.impl;

import cn.cmcc.common.constant.Constants;
import cn.cmcc.common.constant.HttpStatus;
import cn.cmcc.common.exception.CustomException;
import cn.cmcc.common.storage.config.S3Properties;
import cn.cmcc.common.storage.service.EnhancedRetryExecutor;
import cn.cmcc.common.storage.utils.LocalStorageUtil;
import cn.cmcc.common.utils.DateUtils;
import cn.cmcc.common.utils.StringUtils;
import cn.cmcc.common.utils.WebUtils;
import cn.cmcc.common.utils.file.FileUtils;
import cn.cmcc.common.utils.file.MimeTypeUtils;
import cn.cmcc.common.utils.uuid.IdUtils;
import cn.cmcc.common.web.domain.AjaxResult;
import cn.hutool.core.lang.Assert;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.retry.PredefinedRetryPolicies;
import com.amazonaws.retry.RetryPolicy;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Service
@Slf4j
public class StorageService {

	public static final String SEPARATOR = "/";

	@Autowired
	private S3Properties s3Properties;

	@Autowired
	private EnhancedRetryExecutor retryExecutor;

	private AmazonS3 client;

	@PostConstruct
	public void init() {
		AwsClientBuilder.EndpointConfiguration endpointConfig = new AwsClientBuilder.EndpointConfiguration(
				s3Properties.getEndpoint(), s3Properties.getRegion());

		AWSCredentials credentials = new BasicAWSCredentials(s3Properties.getAccessKey(), s3Properties.getSecretKey());
		AWSCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);

        // 完善的客户端配置
		ClientConfiguration clientConfig = new ClientConfiguration();

        // 连接池配置
		clientConfig.setMaxConnections(s3Properties.getMaxConnections());

        // 超时配置
        clientConfig.setConnectionTimeout(s3Properties.getConnectionTimeout());
        clientConfig.setSocketTimeout(s3Properties.getSocketTimeout());
        clientConfig.setRequestTimeout(s3Properties.getRequestTimeout());

        // 连接管理配置
        clientConfig.setConnectionMaxIdleMillis(s3Properties.getMaxIdleTime());
        clientConfig.setValidateAfterInactivityMillis(s3Properties.getValidateAfterInactivity());

        // HTTP 配置优化
        clientConfig.setUseExpectContinue(s3Properties.getUseExpectContinue());
        clientConfig.setUseGzip(s3Properties.getUseGzip());

        // 协议配置
		if (s3Properties.isHttps()) {
			clientConfig.setProtocol(Protocol.HTTPS);
		} else {
			clientConfig.setProtocol(Protocol.HTTP);
		}

        // 重试策略配置
        RetryPolicy retryPolicy = new RetryPolicy(
                PredefinedRetryPolicies.DEFAULT_RETRY_CONDITION,
                PredefinedRetryPolicies.DEFAULT_BACKOFF_STRATEGY,
                s3Properties.getMaxRetries(),
                true
        );
        clientConfig.setRetryPolicy(retryPolicy);

        // 构建客户端
        AmazonS3ClientBuilder build = AmazonS3Client.builder()
                .withEndpointConfiguration(endpointConfig)
                .withClientConfiguration(clientConfig)
                .withCredentials(credentialsProvider)
                .disableChunkedEncoding();

		/**
		 * 域名访问要设置DEFAULT_PATH_STYLE_ACCESS 为true
		 */
		build.setPathStyleAccessEnabled(true);
		this.client = build.build();

        log.info("AWS S3 客户端初始化完成 - 连接超时:{}ms, 套接字超时:{}ms, 请求超时:{}ms, 最大重试:{}",
                s3Properties.getConnectionTimeout(), s3Properties.getSocketTimeout(),
                s3Properties.getRequestTimeout(), s3Properties.getMaxRetries());

		createBucket(s3Properties.getBucketName());
	}

	@SneakyThrows
	public void createBucket(String bucketName) {
		if (!bucketName.toLowerCase().equals(bucketName)) {
			throw new RuntimeException("bucket name not allow upper case");
		}
		if (checkBucketExist(bucketName)) {
			log.info("bucket: {} 已经存在", bucketName);
			return;
		}
		client.createBucket((bucketName));

	}

	@SneakyThrows
	public boolean checkBucketExist(String bucketName) {
		return client.doesBucketExistV2(bucketName);
	}

	@SneakyThrows
	public List<Bucket> getAllBuckets() {
		return client.listBuckets();
	}

	@SneakyThrows
	public Optional<Bucket> getBucket(String bucketName) {
		return client.listBuckets().stream().filter(b -> b.getName().equals(bucketName)).findFirst();
	}

	@SneakyThrows
	public void removeBucket(String bucketName) {
		client.deleteBucket(bucketName);
	}

	/**
	 * 文件重命名(文件名后缀也要保持不变)
	 */
	public void renameFile(String srcFileName,String newName) {
		CopyObjectRequest copyReq = new CopyObjectRequest(s3Properties.getBucketName(),srcFileName,
				s3Properties.getBucketName(),newName);
		client.copyObject(copyReq);
		deleteFile(s3Properties.getBucketName(),srcFileName);
	}

	/**
	 * 文件交换（文件名后缀也要保持不变）
	 */
	public void exhangeFile(String srcFileName,String newName) {
		CopyObjectRequest bakReq = new CopyObjectRequest(s3Properties.getBucketName(),srcFileName,
				s3Properties.getBucketName(),srcFileName + "_bak");
		client.copyObject(bakReq);
		renameFile(newName,srcFileName);
		renameFile(srcFileName + "_bak",newName);
	}

	/**
	 * 复制文件
	 */
	public void copyFile(String bucketName, String srcFileName, String tarFileName) {
		client.copyObject(bucketName, srcFileName, bucketName, tarFileName);
	}

	/**
	 * 删除文件
	 */
	@SneakyThrows
	public void deleteFile(String bucketName, String fileName) {
		client.deleteObject(bucketName, fileName);
	}

	@SneakyThrows
	public String upload(MultipartFile file) {
		String fileName = extractFilename(file).replace("\\", "/");
		if (fileName.startsWith(SEPARATOR))
			fileName = fileName.replaceFirst(SEPARATOR, "");
		InputStream inputStream = new ByteArrayInputStream(file.getBytes());
		ObjectMetadata metadata = new ObjectMetadata();
		metadata.setContentType("application/octet-stream");
		metadata.setContentLength(inputStream.available());
		PutObjectRequest putObjectRequest = new PutObjectRequest(s3Properties.getBucketName(), fileName,
				inputStream, metadata);

        // 使用带重试的上传方法
        putObjectWithRetry(putObjectRequest, "文件上传");
		String fileUrl = getFileUrl(fileName);
        log.info("文件上传成功: {} -> {}", file.getOriginalFilename(), fileUrl);
		return fileUrl;

	}

	@SneakyThrows
	public UploadResult upload(String basePath, MultipartFile file, String fileName) {
		String filePathName = extractFilename(basePath, file, fileName);
		if (filePathName.startsWith(SEPARATOR))
			filePathName = filePathName.replaceFirst(SEPARATOR, "");

		return upload(file.getBytes(),filePathName);
	}

	/**
	 * 右斜杠在minio中不会建目录，替换成左斜杠
	 *
	 * @param buf
	 * @param s3Path  例：
	 * @return
	 * UploadResult
	 */
	@SneakyThrows
	public UploadResult upload(byte buf[],String s3Path) {
		s3Path = s3Path.replace("\\", "/");
		try(InputStream inputStream = new ByteArrayInputStream(buf)){
			ObjectMetadata metadata = new ObjectMetadata();
			metadata.setContentType("application/octet-stream");
			metadata.setContentLength(inputStream.available());

			PutObjectRequest putObjectRequest = new PutObjectRequest(s3Properties.getBucketName(), s3Path, inputStream, metadata);

            // 使用带重试的上传方法
            PutObjectResult putObjectResult = putObjectWithRetry(putObjectRequest, "字节数组上传");
            log.info("上传对象成功: {}, MD5: {}", s3Path, putObjectResult.getContentMd5());
			return new UploadResult(getFileUrl(s3Path), putObjectResult.getContentMd5());
		}
	}

	@SneakyThrows
	public UploadResult upload(InputStream inputStream, String s3Path) {
		s3Path = s3Path.replace("\\", "/");
		ObjectMetadata metadata = new ObjectMetadata();
		metadata.setContentType("application/octet-stream");
		metadata.setContentLength(inputStream.available());

		PutObjectRequest putObjectRequest = new PutObjectRequest(s3Properties.getBucketName(), s3Path,
				inputStream, metadata);

        // 使用带重试的上传方法
        PutObjectResult putObjectResult = putObjectWithRetry(putObjectRequest, "输入流上传");
        log.info("输入流上传成功: {}, MD5: {}", s3Path, putObjectResult.getContentMd5());
		return new UploadResult(getFileUrl(s3Path), putObjectResult.getContentMd5());
	}

	@SneakyThrows
	public UploadResult upload(String remoteUrl,String s3Path) {
		try (ByteArrayOutputStream fos = new ByteArrayOutputStream() ) {
        	HttpUtil.download(remoteUrl, fos, false);
        	return upload(fos.toByteArray(),s3Path);
        }
	}

	@SneakyThrows
	public UploadResult upload(File file, String fileName) {
		return upload(file, fileName, true);
	}

	@SneakyThrows
	public UploadResult upload(File file, String fileName, boolean delLocalFile) {
		long length = file.length();
		PutObjectRequest putObjectRequest = new PutObjectRequest(s3Properties.getBucketName(), fileName, file);

        // 使用带重试的上传方法
        PutObjectResult putObjectResult = putObjectWithRetry(putObjectRequest, "文件上传");
        log.info("文件上传成功: {} -> {}, 大小: {}字节", file.getName(), fileName, length);

        if(delLocalFile) {
        	FileUtils.deleteFile(file);
        }
		return new UploadResult(getFileUrl(fileName), putObjectResult.getContentMd5());
	}
	/**
	 * <AUTHOR> @date: 2024/8/29 上午11:58
	 * @description: 携带token下载文件
	 */
	@SneakyThrows
	public UploadResult upload(String remoteUrl,String s3Path,String token) {
		try (ByteArrayOutputStream fos = new ByteArrayOutputStream() ) {
			Assert.notBlank(remoteUrl, "[url] is blank !", new Object[0]);
			HttpResponse response = HttpUtil.createGet(remoteUrl, true).addHeaders(Map.of("Authorization",token)).timeout(-1).executeAsync();
			if (!response.isOk()) {
				throw new CustomException("携带token下载文件失败.远程路径:"+remoteUrl);
			}
			response.writeBody(fos, false, null);
			return upload(fos.toByteArray(),s3Path);
		}
	}

	public boolean deleteFile(String filePath) {
		try {
			String localFileName = LocalStorageUtil.getPhysicalPath(filePath);
			if(StringUtils.isNotEmpty(localFileName)) {
				FileUtils.deleteFile(localFileName);
			}
			if(filePath.startsWith(Constants.RESOURCE_PREFIX)) {
				filePath = filePath.replaceFirst(Constants.RESOURCE_PREFIX + SEPARATOR, "");
			}else {
				filePath = filePath.replaceFirst(SEPARATOR + s3Properties.getBucketName() + SEPARATOR, "");
			}
			client.deleteObject(s3Properties.getBucketName(), filePath);
			return true;
		} catch (Exception e) {
			log.error("AmazonS3 deleteFile error:",e);
		}
		return false;
	}

	/**
	 * 历史的附件以/attement开头，直接去掉
	 *
	 * @param remoteFilePath
	 * @param response
	 * void
	 */
	public void downLoad(String remoteFilePath, HttpServletResponse response) {
		try {
			if (remoteFilePath.startsWith(Constants.RESOURCE_PREFIX)) {
				remoteFilePath = remoteFilePath.replaceFirst(Constants.RESOURCE_PREFIX + SEPARATOR, "");
			} else {
				remoteFilePath = remoteFilePath.replaceFirst(SEPARATOR + s3Properties.getBucketName() + SEPARATOR, "");
			}

            // 使用带重试的下载方法获取 S3Object
            try (S3Object object = getObjectWithRetry(s3Properties.getBucketName(), remoteFilePath, "响应流下载")) {
				if (object == null) {
					WebUtils.renderString(response, JSONObject.toJSONString(AjaxResult.error(HttpStatus.NOT_FOUND, "文件不存在！")));
					return;
				}

				// 检查客户端连接是否已断开
				if (response.isCommitted()) {
					log.debug("客户端连接已断开，停止下载: {}", remoteFilePath);
					return;
				}

				try (S3ObjectInputStream inputStream = object.getObjectContent()) {
					IOUtils.copy(inputStream, response.getOutputStream());
				}
			}
		} catch (IOException e) {
			// 检查是否为客户端断开连接异常
			if (isClientDisconnectedException(e)) {
				log.debug("客户端断开连接，下载中止: {}", remoteFilePath);
			} else {
				log.error("下载文件失败: {}", remoteFilePath, e);
				throw new RuntimeException("下载文件失败", e);
			}
		} catch (Exception e) {
			log.error("下载文件异常: {}", remoteFilePath, e);
			throw new RuntimeException("下载文件异常", e);
		}
	}

	public void downLoad(String remoteFilePath, OutputStream os) {
		String localFileName = LocalStorageUtil.getPhysicalPath(remoteFilePath);
		if(StringUtils.isNotEmpty(localFileName)) {
			try {
				//资源所有权问题：谁创建的资源，谁负责关闭。FileUtils.writeBytes 作为工具方法，应该专注于数据传输功能，而不应该越权管理调用者的资源
				FileUtils.writeBytes(localFileName, os);
			} catch (IOException e) {
				if (isClientDisconnectedException(e)) {
					log.debug("客户端断开连接，本地文件下载中止: {}", remoteFilePath);
				} else {
					log.error("本地文件下载失败: {}", remoteFilePath, e);
					throw new RuntimeException("本地文件下载失败", e);
				}
			}
			return;
		}

		try {
			if (remoteFilePath.startsWith(Constants.RESOURCE_PREFIX)) {
				remoteFilePath = remoteFilePath.replaceFirst(Constants.RESOURCE_PREFIX + SEPARATOR, "");
			} else {
				remoteFilePath = remoteFilePath.replaceFirst(SEPARATOR + s3Properties.getBucketName() + SEPARATOR, "");
			}

            // 使用带重试的下载方法获取 S3Object
            try (S3Object object = getObjectWithRetry(s3Properties.getBucketName(), remoteFilePath, "输出流下载")) {
				if (object == null) {
					throw new RuntimeException("文件不存在: " + remoteFilePath);
				}

				try (S3ObjectInputStream inputStream = object.getObjectContent()) {
					IOUtils.copy(inputStream, os);
				}
			}
		} catch (IOException e) {
			// 检查是否为客户端断开连接异常
			if (isClientDisconnectedException(e)) {
				log.debug("客户端断开连接，下载中止: {}", remoteFilePath);
			} else {
				log.error("下载文件失败: {}", remoteFilePath, e);
				throw new RuntimeException("下载文件失败", e);
			}
		} catch (Exception e) {
			log.error("下载文件异常: {}", remoteFilePath, e);
			throw new RuntimeException("下载文件异常", e);
		}
	}

	public void downLoad(String remoteFilePath, String localFilePath) {
		try {
			if (remoteFilePath.startsWith(Constants.RESOURCE_PREFIX)) {
				remoteFilePath = remoteFilePath.replaceFirst(Constants.RESOURCE_PREFIX + SEPARATOR, "");
			} else {
				remoteFilePath = remoteFilePath.replaceFirst(SEPARATOR + s3Properties.getBucketName() + SEPARATOR, "");
			}

            // 使用带重试的下载方法获取 S3Object
            try (S3Object object = getObjectWithRetry(s3Properties.getBucketName(), remoteFilePath, "本地文件下载")) {
				if (object == null) {
					throw new RuntimeException("文件不存在: " + remoteFilePath);
				}

				File localFile = FileUtils.getFile(localFilePath);
				if (!localFile.getParentFile().exists()) {
					localFile.getParentFile().mkdirs();
				}

				try (S3ObjectInputStream inputStream = object.getObjectContent()) {
					FileUtils.copyInputStreamToFile(inputStream, localFile);
				}
			}
		} catch (IOException e) {
			log.error("下载文件到本地失败: {} -> {}", remoteFilePath, localFilePath, e);
			throw new RuntimeException("下载文件到本地失败", e);
		} catch (Exception e) {
			log.error("下载文件到本地异常: {} -> {}", remoteFilePath, localFilePath, e);
			throw new RuntimeException("下载文件到本地异常", e);
		}
	}

	/**
	 * 获取文件元数据
	 *
	 * @param path 完整文件路径
	 */
	public ObjectMetadata getObjectMetadata(String path) {
		path = path.replace(SEPARATOR + s3Properties.getBucketName() + SEPARATOR, "");
        try (S3Object object = getObjectWithRetry(s3Properties.getBucketName(), path, "获取文件元数据")) {
            return object.getObjectMetadata();
        } catch (IOException e) {
            log.error("获取文件元数据失败: {}", path, e);
            throw new RuntimeException("获取文件元数据失败", e);
        }
	}

	/**
	 * 由于现在附件图片都走接口请求获取，前缀统一为/attement
	 *
	 * @param fileName
	 * @return
	 * String
	 */
	private String getFileUrl(String fileName) {
		// 使用相对路径
		return Constants.RESOURCE_PREFIX + SEPARATOR + fileName;
	}

	/**
	 * 获取文件名的后缀
	 *
	 * @param file 表单文件
	 * @return 后缀名
	 */
	public String getExtension(MultipartFile file) {
		String extension = FilenameUtils.getExtension(file.getOriginalFilename());
		if (StringUtils.isEmpty(extension)) {
			extension = MimeTypeUtils.getExtension(file.getContentType());
		}
		return extension;
	}

	/**
	 * 编码文件名
	 */
	public String extractFilename(MultipartFile file) {
		String fileName = file.getOriginalFilename().replace("#", "");
		fileName = DateUtils.datePath() + SEPARATOR + IdUtils.fastSimpleUUID() + "_" + fileName;
		return fileName;
	}

	/**
	 * 编码文件名
	 */
	public String extractFilename(String basePathName, MultipartFile file, String realFileName) {
		if (StringUtils.isEmpty(basePathName)) {
			basePathName = DateUtils.datePath();
		}
		String fileName = "";
		if (StringUtils.isEmpty(realFileName)) {
			fileName = basePathName + SEPARATOR + IdUtils.fastSimpleUUID() + "_"
					+ file.getOriginalFilename().replace("#", "");
		} else {
			// 文件名太长了，转md5
			if(realFileName.length() < 100){
				fileName = basePathName + SEPARATOR + realFileName.replace("#", "") + "." + getExtension(file);
			}else{
				realFileName = MD5.create().digestHex16(realFileName);
				fileName = basePathName + SEPARATOR + (realFileName + "_" + file.getOriginalFilename()).replace("#", "");
			}
		}

		return fileName;
	}

	/**
	 * 列出对象
	 *
	 * @param prefix 前缀
	 * @return 对象
	 */
	public List<S3ObjectSummary> listObjects(String prefix) {
		boolean top = prefix.isEmpty() || prefix.equals(SEPARATOR);
		if (!prefix.endsWith(SEPARATOR)) {
			prefix += SEPARATOR;
		}

		ListObjectsRequest request = top ?
				new ListObjectsRequest().withBucketName(s3Properties.getBucketName()).withDelimiter(SEPARATOR).withMaxKeys(1000) :
				new ListObjectsRequest().withBucketName(s3Properties.getBucketName()).withPrefix(prefix).withDelimiter(SEPARATOR).withMaxKeys(1000);
		ObjectListing objects = client.listObjects(request);
		return objects.getObjectSummaries();
	}

	/**
	 * 上传
	 *
	 * @param log       日志
	 * @param threshold 目录阈值（兆），-1：默认值
	 * @param prefix    前缀
	 * @param object    对象
	 * @param key       键
	 * @return 上传结果
	 */
	public UploadResult upload(Logger log, long threshold, String prefix, Object object, String key) throws IOException {
		if (object == null) return null;

		// 删除旧文件
		CompletableFuture.runAsync(() -> {
			List<S3ObjectSummary> objects = listObjects(prefix);
			if (objects == null || objects.isEmpty()) return;

			// 容量阈值/数量阈值
			long thresholdBytes = (threshold == -1L ? 4096L : threshold) * 1024L * 1024L, thresholdAmount = 1024L;
			AtomicLong bytes = new AtomicLong(0L), amount = new AtomicLong(0L);
			objects.stream().sorted(Comparator.comparing(it -> -it.getLastModified().getTime())).forEach(it -> {
				if ((bytes.addAndGet(it.getSize()) > thresholdBytes || amount.addAndGet(1L) > thresholdAmount)) {
					deleteFile(it.getBucketName(), it.getKey());
					log.info("Total: {}, Delete File: {}, {}.", bytes.get(), it.getKey(), it.getLastModified());
				}
			});
		});

		if (!key.startsWith(prefix)) key = (prefix.endsWith(SEPARATOR) ? prefix : prefix + SEPARATOR) + key;
		if (object instanceof byte[]) {
			return upload((byte[]) object, key);
		} else if (object instanceof InputStream) {
			return upload((InputStream) object, key);
		} else if (object instanceof String) {
			return upload((String) object, key);
		} else if (object instanceof File) {
			return upload((File) object, key);
		}
		throw new RuntimeException(String.format("Unsupported types [%s]", object.getClass().getTypeName()));
	}

	public URL generatePresignedUrl(String objectName, long duration, TimeUnit unit) {
		GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(s3Properties.getBucketName(), objectName);
		return client.generatePresignedUrl(generatePresignedUrlRequest.withExpiration(new Date(System.currentTimeMillis() + unit.toMillis(duration))));
	}

	/**
	 * 检查远程文件是否可下载非空且未损坏
	 *
	 * @param remoteFilePath 远程文件路径
	 * @return 如果文件可下载则返回true，否则返回false
	 */
	public boolean checkFileDownloadable(String remoteFilePath) {
		// 检查 remoteFilePath 是否为空或无效
		log.info("文件地址为:{}", remoteFilePath);
		if (remoteFilePath == null || remoteFilePath.isEmpty()) {
			log.info("文件地址为空!");
			return false;
		}
		int queryIndex = remoteFilePath.indexOf('?');
		if (queryIndex != -1) {
			remoteFilePath = remoteFilePath.substring(0, queryIndex);
		}
		// 处理文件路径前缀
		if(remoteFilePath.startsWith(Constants.RESOURCE_PREFIX)) {
			remoteFilePath = remoteFilePath.replaceFirst(Constants.RESOURCE_PREFIX + SEPARATOR, "");
		}else {
			remoteFilePath = remoteFilePath.replaceFirst(SEPARATOR + s3Properties.getBucketName() + SEPARATOR, "");
		}
		try {
            // 使用带重试的方法获取 S3 对象
            try (S3Object object = getObjectWithRetry(s3Properties.getBucketName(), remoteFilePath, "检查文件可下载性")) {
                // 检查对象是否存在
                if (object == null) {
                    log.info("文件不存在");
                    return false;
                }
                // 检查文件大小是否为0KB
                long contentLength = object.getObjectMetadata().getContentLength();
                if (contentLength <= 0) {
                    log.info("文件为0KB");
                    return false;
                }
                // 尝试读取文件的一部分内容，确保文件没有损坏
                try (S3ObjectInputStream inputStream = object.getObjectContent()) {
                    byte[] buffer = new byte[1024];
                    int bytesRead = inputStream.read(buffer);
                    if (bytesRead < 0) {
                        return false; // 文件为空或无法读取
                    }
                } catch (Exception e) {
                    log.info("文件读取失败");
                    throw new RuntimeException(e);
                }
			}
		} catch (Exception e) {
			log.info("文件下载失败", e);
			// 处理其他异常
			return false;
		}
		return true;
	}

	/**
	 * 检查是否为客户端断开连接异常
	 *
	 * @param e 异常
	 * @return 如果是客户端断开连接异常返回true，否则返回false
	 */
	private boolean isClientDisconnectedException(IOException e) {
		if (e == null) {
			return false;
		}

		String message = e.getMessage();
		if (message == null) {
			return false;
		}

		// 检查常见的客户端断开连接异常消息
		String lowerMessage = message.toLowerCase();
		return lowerMessage.contains("broken pipe") ||
				lowerMessage.contains("connection reset") ||
				lowerMessage.contains("connection aborted") ||
				lowerMessage.contains("socket closed") ||
				lowerMessage.contains("connection closed") ||
				lowerMessage.contains("远程主机强迫关闭了一个现有的连接");
    }

    /**
     * 带重试的上传方法
     *
     * @param putObjectRequest 上传请求
     * @param operation        操作描述
     * @return 上传结果
     */
    private PutObjectResult putObjectWithRetry(PutObjectRequest putObjectRequest, String operation) {
        return retryExecutor.executeWithRetry("upload",
                () -> client.putObject(putObjectRequest),
                operation);
    }

    /**
     * 检查是否为可重试的网络错误
     */
    private boolean isRetryableNetworkError(String errorMessage) {
        if (errorMessage == null) {
            return false;
        }

        String lowerMessage = errorMessage.toLowerCase();
        return lowerMessage.contains("connection reset") ||
                lowerMessage.contains("connection timeout") ||
                lowerMessage.contains("read timeout") ||
                lowerMessage.contains("socket timeout") ||
                lowerMessage.contains("connection refused") ||
                lowerMessage.contains("network is unreachable") ||
                lowerMessage.contains("broken pipe") ||
                lowerMessage.contains("connection aborted");
    }

    /**
     * 计算重试延迟时间（指数退避）
     */
    private long calculateRetryDelay(int attempt) {
        // 基础延迟 1 秒，指数退避，最大 30 秒
        long baseDelay = 1000;
        long maxDelay = 30000;
        long delay = baseDelay * (1L << (attempt - 1));
        return Math.min(delay, maxDelay);
    }

    /**
     * 带重试的下载方法 - 获取S3对象
     *
     * @param bucketName 存储桶名称
     * @param key        对象键
     * @param operation  操作描述
     * @return S3对象
     */
    private S3Object getObjectWithRetry(String bucketName, String key, String operation) {
        return retryExecutor.executeWithRetry("download",
                () -> client.getObject(bucketName, key),
                operation);
    }

	@Data
	@AllArgsConstructor
	public static class UploadResult {
		String fileUrl;
		String md5Sum;
	}

}
