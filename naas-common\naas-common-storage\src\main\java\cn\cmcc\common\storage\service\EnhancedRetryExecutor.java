package cn.cmcc.common.storage.service;

import cn.cmcc.common.storage.config.RetryConfig;
import cn.cmcc.common.storage.config.RetryStrategy;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.function.Supplier;

/**
 * 增强的重试执行器
 * 支持多种重试策略、自适应调整、指标统计等
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
@Slf4j
@Service
public class EnhancedRetryExecutor {
    
    @Autowired
    private RetryConfig retryConfig;
    
    @Autowired
    private RetryMetricsService metricsService;
    
    private final Random random = new Random();
    
    /**
     * 执行带重试的操作
     * 
     * @param operationType 操作类型（upload, download, metadata等）
     * @param operation 要执行的操作
     * @param operationDescription 操作描述（用于日志）
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public <T> T executeWithRetry(String operationType, Supplier<T> operation, String operationDescription) {
        RetryConfig.RetrySettings settings = retryConfig.getSettingsForOperation(operationType);
        long startTime = System.currentTimeMillis();
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= settings.getMaxRetries() + 1; attempt++) {
            try {
                // 记录重试开始（除了第一次尝试）
                if (attempt > 1) {
                    metricsService.recordRetryStart(operationType, attempt);
                }
                
                T result = operation.get();
                
                // 记录成功
                if (attempt > 1) {
                    long totalDuration = System.currentTimeMillis() - startTime;
                    metricsService.recordRetrySuccess(operationType, attempt, totalDuration);
                    log.info("{}重试成功，第{}次尝试，总耗时{}ms", operationDescription, attempt, totalDuration);
                }
                
                return result;
                
            } catch (AmazonServiceException e) {
                lastException = e;
                // AWS 服务异常，检查是否有特定的重试配置
                String errorType = "aws_service_" + e.getErrorCode();
                RetryConfig.RetrySettings errorSettings = retryConfig.getSettingsForError(errorType);
                
                if (errorSettings == null) {
                    // 没有特定配置，通常不重试AWS服务异常
                    long totalDuration = System.currentTimeMillis() - startTime;
                    metricsService.recordRetryFailure(operationType, attempt, errorType, totalDuration);
                    log.error("{}失败，AWS服务异常: {} (状态码: {})", operationDescription, e.getMessage(), e.getStatusCode());
                    throw new RuntimeException(operationDescription + "失败: " + e.getMessage(), e);
                }
                
                // 有特定配置，按配置重试
                if (attempt <= errorSettings.getMaxRetries()) {
                    long delay = calculateDelay(attempt, errorSettings, operationType);
                    log.warn("{}失败，第{}次尝试，{}ms后重试。AWS服务异常: {}", 
                            operationDescription, attempt, delay, e.getMessage());
                    sleep(delay);
                    continue;
                }
                
            } catch (SdkClientException e) {
                lastException = e;
                String errorMessage = e.getMessage();
                String errorType = classifyNetworkError(errorMessage);
                
                // 检查是否为可重试的网络错误
                if (isRetryableNetworkError(errorMessage)) {
                    if (attempt <= settings.getMaxRetries()) {
                        long delay = calculateDelay(attempt, settings, operationType);
                        log.warn("{}失败，第{}次尝试，{}ms后重试。错误: {}", 
                                operationDescription, attempt, delay, errorMessage);
                        sleep(delay);
                        continue;
                    }
                }
                
                // 不可重试的错误或重试次数用尽
                long totalDuration = System.currentTimeMillis() - startTime;
                metricsService.recordRetryFailure(operationType, attempt, errorType, totalDuration);
                log.error("{}失败，第{}次尝试，不再重试。错误: {}", operationDescription, attempt, errorMessage);
                throw new RuntimeException(operationDescription + "失败: " + errorMessage, e);
                
            } catch (Exception e) {
                lastException = e;
                String errorType = e.getClass().getSimpleName();
                long totalDuration = System.currentTimeMillis() - startTime;
                metricsService.recordRetryFailure(operationType, attempt, errorType, totalDuration);
                log.error("{}失败，未知异常: {}", operationDescription, e.getMessage());
                throw new RuntimeException(operationDescription + "失败: " + e.getMessage(), e);
            }
        }
        
        // 理论上不会到达这里
        long totalDuration = System.currentTimeMillis() - startTime;
        metricsService.recordRetryFailure(operationType, settings.getMaxRetries() + 1, "max_retries_exceeded", totalDuration);
        throw new RuntimeException(operationDescription + "失败，重试次数用尽", lastException);
    }
    
    /**
     * 计算重试延迟时间
     */
    private long calculateDelay(int attempt, RetryConfig.RetrySettings settings, String operationType) {
        double successRate = metricsService.getSuccessRate(operationType);
        long baseDelay = settings.getStrategy() == RetryStrategy.FIXED_DELAY ? 
                settings.getFixedDelay() : settings.getBaseDelay();
        
        long delay = settings.getStrategy().calculateDelay(attempt, baseDelay, settings.getMaxDelay(), successRate);
        
        // 添加抖动
        if (settings.isEnableJitter()) {
            double jitter = 1.0 + (random.nextDouble() - 0.5) * 2 * settings.getJitterFactor();
            delay = (long) (delay * jitter);
        }
        
        return Math.max(delay, 0);
    }
    
    /**
     * 检查是否为可重试的网络错误
     */
    private boolean isRetryableNetworkError(String errorMessage) {
        if (errorMessage == null) {
            return false;
        }
        
        String lowerMessage = errorMessage.toLowerCase();
        return lowerMessage.contains("connection reset") ||
                lowerMessage.contains("connection timeout") ||
                lowerMessage.contains("read timeout") ||
                lowerMessage.contains("socket timeout") ||
                lowerMessage.contains("connection refused") ||
                lowerMessage.contains("network is unreachable") ||
                lowerMessage.contains("broken pipe") ||
                lowerMessage.contains("connection aborted");
    }
    
    /**
     * 分类网络错误类型
     */
    private String classifyNetworkError(String errorMessage) {
        if (errorMessage == null) {
            return "unknown_error";
        }
        
        String lowerMessage = errorMessage.toLowerCase();
        if (lowerMessage.contains("connection timeout")) {
            return "connection_timeout";
        } else if (lowerMessage.contains("read timeout")) {
            return "read_timeout";
        } else if (lowerMessage.contains("socket timeout")) {
            return "socket_timeout";
        } else if (lowerMessage.contains("connection reset")) {
            return "connection_reset";
        } else if (lowerMessage.contains("connection refused")) {
            return "connection_refused";
        } else if (lowerMessage.contains("network is unreachable")) {
            return "network_unreachable";
        } else if (lowerMessage.contains("broken pipe")) {
            return "broken_pipe";
        } else if (lowerMessage.contains("connection aborted")) {
            return "connection_aborted";
        } else {
            return "network_error";
        }
    }
    
    /**
     * 安全的睡眠方法
     */
    private void sleep(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("操作被中断", e);
        }
    }
}
