# 📋 StorageService 测试运行指南

## 🚀 **快速开始**

### **1. 环境准备**
```bash
# 进入 naas-plan-base 模块目录
cd naas-modules/naas-plan/naas-plan-base

# 确保Maven依赖已安装
mvn clean install -DskipTests
```

### **2. 运行基础功能测试**
```bash
# 推荐：运行单元测试（最快，无依赖）
mvn test -Dtest=StorageServiceUnitTest

# 运行增强重试功能测试（已优化，无Spring Boot依赖）
mvn test -Dtest=StorageServiceEnhancedRetryTest

# 运行性能测试（已优化，无Spring Boot依赖）
mvn test -Dtest=StorageServicePerformanceTest

# 或者使用脚本一键运行所有测试
./run-tests.sh    # Linux/Mac
run-tests.bat     # Windows
```

### **3. 运行集成测试（需要真实S3服务）**

#### **启动MinIO服务**
```bash
# 使用Docker启动MinIO
docker run -p 9000:9000 -p 9001:9001 \
  -e "MINIO_ROOT_USER=cmcc_minio" \
  -e "MINIO_ROOT_PASSWORD=cmcc_minio" \
  minio/minio server /data --console-address ":9001"
```

#### **设置环境变量**
```bash
# Linux/Mac
export INTEGRATION_TEST_ENABLED=true
export INTEGRATION_S3_ENDPOINT=http://localhost:9000
export INTEGRATION_S3_ACCESS_KEY=cmcc_minio
export INTEGRATION_S3_SECRET_KEY=cmcc_minio
export INTEGRATION_S3_BUCKET=integration-test-bucket

# Windows
set INTEGRATION_TEST_ENABLED=true
set INTEGRATION_S3_ENDPOINT=http://localhost:9000
set INTEGRATION_S3_ACCESS_KEY=cmcc_minio
set INTEGRATION_S3_SECRET_KEY=cmcc_minio
set INTEGRATION_S3_BUCKET=integration-test-bucket
```

#### **运行集成测试**
```bash
mvn test -Dtest=StorageServiceIntegrationTest -Dspring.profiles.active=integration-test
```

## 📁 **配置文件说明**

### **测试配置文件位置**
```
naas-plan-base/src/test/resources/
├── application-test.yml              # 基础测试配置
├── application-integration-test.yml  # 集成测试配置
└── README-测试运行指南.md            # 本文件
```

### **主配置文件位置**
```
naas-plan-base/src/main/resources/
├── application-storage-enhanced.yml  # 存储增强配置
└── application-integration-test.yml  # 集成测试主配置
```

## 🧪 **测试类说明**

### **StorageServiceEnhancedRetryTest**
- **功能**：测试增强重试功能
- **覆盖**：重试策略、配置灵活性、指标统计
- **运行时间**：约30秒

### **StorageServicePerformanceTest**
- **功能**：性能和压力测试
- **覆盖**：大文件处理、并发测试、内存使用
- **运行时间**：约2-5分钟

### **StorageServiceIntegrationTest**
- **功能**：真实S3环境集成测试
- **覆盖**：实际网络环境、配置验证
- **运行时间**：约1-3分钟
- **前提**：需要运行的S3服务

## 🔧 **故障排查**

### **常见问题**

#### **1. 配置文件加载失败**
```
错误：Could not resolve placeholder 's3.endpoint'
解决：确保 application-test.yml 在 src/test/resources 目录中
```

#### **2. Bean创建失败**
```
错误：No qualifying bean of type 'RetryConfig'
解决：检查 StorageTestConfiguration 是否正确加载
```

#### **3. 集成测试连接失败**
```
错误：Connection refused to localhost:9000
解决：
1. 确保MinIO服务正在运行
2. 检查端口是否被占用
3. 验证环境变量设置
```

#### **4. 测试超时**
```
错误：Test timed out
解决：在 application-test.yml 中增加超时时间
```

### **调试技巧**

#### **启用详细日志**
```yaml
# 在测试配置中添加
logging:
  level:
    cn.cmcc.common.storage: DEBUG
    org.springframework: DEBUG
```

#### **检查配置加载**
```java
@Test
void debugConfiguration() {
    System.out.println("S3 Endpoint: " + s3Properties.getEndpoint());
    System.out.println("Retry Config: " + retryConfig.getDefaultSettings());
}
```

## 📊 **性能基准**

### **预期测试结果**

#### **基础功能测试**
- 所有测试方法应该通过
- 运行时间 < 30秒
- 无内存泄漏

#### **性能测试**
- 大文件(100MB)上传 < 10秒
- 50个并发请求 < 5秒
- 内存使用合理

#### **集成测试**
- 真实S3操作成功
- 重试机制生效
- 指标统计准确

## 🎯 **测试检查清单**

### **运行前检查**
- [ ] Maven依赖已安装
- [ ] 配置文件存在于正确位置
- [ ] 环境变量已设置（集成测试）
- [ ] MinIO服务已启动（集成测试）

### **运行后验证**
- [ ] 所有测试用例通过
- [ ] 无异常日志输出
- [ ] 性能指标符合预期
- [ ] 内存使用正常

## 📞 **获取帮助**

如果遇到问题，请检查：
1. 日志输出中的详细错误信息
2. 配置文件是否正确加载
3. 依赖是否完整安装
4. 网络连接是否正常

测试愉快！🎉
