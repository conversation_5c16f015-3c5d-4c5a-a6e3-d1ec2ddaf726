<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.cmcc</groupId>
        <artifactId>naas-plan</artifactId>
        <version>3.0.0</version>
    </parent>

    <artifactId>naas-plan-base</artifactId>

    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-flow</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-api-kernel</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-api-auth</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-storage</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-springdoc</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-dict</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-webservice</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-recognition</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-geo</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-import</artifactId>
        </dependency>

        <!-- Docker Java API -->
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java</artifactId>
            <version>3.3.3</version>
        </dependency>

        <!-- WebSocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- Jackson for JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.cmcc</groupId>
            <artifactId>naas-common-collect-logs</artifactId>
            <version>3.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>
