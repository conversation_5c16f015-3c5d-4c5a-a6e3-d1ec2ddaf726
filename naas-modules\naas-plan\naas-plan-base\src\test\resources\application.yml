# 默认测试配置 - 解决启动问题
cmcc:
  name: 端到端覆盖分析平台
  version: v2.8
  redisCache: true
  #文件存储方式，local：本地存储，minio：分布式存储
  storageType: local
  # 文件路径
  profile: /data/naas/cmcc/attement
  analytic: false
  # 权限开关
  checkPerimissonSwitch: true


dubbo:
  application:
    logger: slf4j
    # 元数据中心 local 本地 remote 远程 这里使用远程便于其他服务获取
    metadataType: remote
    # 可选值 interface、instance、all，默认是 all，即接口级地址、应用级地址都注册
    register-mode: instance
    service-discovery:
      # FORCE_INTERFACE，只消费接口级地址，如无地址则报错，单订阅 2.x 地址
      # APPLICATION_FIRST，智能决策接口级/应用级地址，双订阅
      # FORCE_APPLICATION，只消费应用级地址，如无地址则报错，单订阅 3.x 地址
      migration: FORCE_APPLICATION
    name: storage-test-app
  protocol:
    # 每次传输数据包大小默认是8m
    payload: 209715200
    # 线程池大小默认200
    threads: 200
    # 设置为 tri 即可使用 Triple 3.0 新协议
    # 性能对比 dubbo 协议并没有提升 但基于 http2 用于多语言异构等 http 交互场景
    # 使用 dubbo 协议通信
    name: dubbo
    # dubbo 协议端口(-1表示自增端口,从20880开始)
    port: -1
    # 指定dubbo协议注册ip
    # host: ***********
  # 注册中心配置
  registry:
    address: nacos://***********:8848?username=nacos&password=1qaz!QAZ
    group: NAAS_DUBBO
    parameters:
      group: NAAS_DUBBO
      namespace: prod
      # namespace: ${spring.profiles.active}
  #config-center:
  #  group: DUBBO_GROUP
  # 消费者相关配置
  consumer:
    # 结果缓存(LRU算法)
    # 会有数据不一致问题 建议在注解局部开启
    cache: false
    # 支持校验注解
    validation: true
    # 超时时间
    timeout: 60000
    # 初始化检查
    check: false
    loadbalance: customLoadBalance
  scan:
    # 接口实现类扫描
    base-packages: cn.cmcc.**.dubbo
  custom:
    # 全局请求log
    request-log: true
    # info 基础信息 param 参数信息 full 全部
    log-level: info

# 不校验白名单
security:
  ignore:
    whites:
      - /**/logout
      - /**/login
      - /casLogin**
      - /**/sso/**
      - /**/interfaceCont/**
      - /**/bpmn20
      - /**/ssoLogin
      - /**/casLogin
      - /**/casLogout
      - /**/captchaImage
      - /*/v2/api-docs
      - /csrf
      - /service/**
      - /**/attement/**
      - /**/service/**
      - /alarm/**
      - /**/kernel/dynamic/query/guihua_stage_statistics_new
      - /**/ws/**

# 日志配置
logging:
  level:
    cn.cmcc: debug
    cn.cmcc.common.storage: INFO
    cn.cmcc.plan: INFO
    com.amazonaws: WARN
    org.apache.http: WARN
    org.springframework: WARN
    root: WARN

# Spring配置
spring:
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: test
    #include: data-collection
  cloud:
    config:
      # 如果本地配置优先级高，那么 override-none 设置为 true，包括系统环境变量、本地配置文件等配置
      override-none: true
      # 如果想要远程配置优先级高，那么 allow-override 设置为 false，如果想要本地配置优先级高那么 allow-override 设置为 true
      allow-override: true
      # 只有系统环境变量或者系统属性才能覆盖远程配置文件的配置，本地配置文件中配置优先级低于远程配置；注意本地配置文件不是系统属性
      override-system-properties: false
    sentinel:
      enabled: true
      eager: true
      transport:
        port: 8719   #可随意指定一个不冲突的端口，用于跟控制台交流的端口
        dashboard: ***********:8718
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  1024MB
      # 设置总上传的文件大小
      max-request-size:  1024MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  redis:
    host: ***********
    # host: 127.0.0.1
    port: 6379
    password:
    # 指定数据库 默认是0
    database: 1
    timeout: 1000
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: org.postgresql.Driver
    druid:
      # 主库数据源
      master:
        url: ********************************************************************************************************************
        username: postgres
        password: MYY34NTqEtJXuLwV9n4o/N+pMvkNzRb4oS7X97Pvsd77/P8QmA==
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 200
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 300000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT CURRENT_TIMESTAMP
      testWhileIdle: false
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      filter:
        stat:
          enabled: true
          log-slow-sql: true
        wall:
          config:
            multi-statement-allow: true
  autoconfigure:
    exclude:
          - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
          - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
          - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
          - org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration


# MyBatis-plus配置
mybatis-plus:
  mapper-locations: classpath*:cn/cmcc/**/mapping/*.xml
  #type-handlers-package: cn.cmcc.config.datasource.typeHandle
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: cn.cmcc
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    call-setters-on-nulls: true
  global-config:
    banner: false
    enable-sql-runner: true
    db-config:
      #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
      id-type: ASSIGN_ID
      #驼峰下划线转换
      table-underline: true
      #插入策略
      insert-strategy: NOT_NULL
      #更新策略 非空判断（如果传入的参数是“”空字符串或者null，不会插入数据库）
      #update-strategy: NOT_EMPTY
      #查询策略
      select-strategy: NOT_EMPTY


# PageHelper分页插件
pagehelper:
  helperDialect: postgresql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  info:
    # 标题
    title: 'NAAS覆盖端到端接口文档'
    # 描述
    description: 'NAAS覆盖端到端接口文档'
    # 版本
    version: 'V3.0.0'
    # 作者信息
    contact:
      name: xhxk
      email: <EMAIL>
  components:
    # 鉴权方式配置
    security-schemes:
      apiKey:
        type: APIKEY
        in: HEADER
        name: ${sa-token.token-name}
  # 服务文档路径映射 参考 gateway router 配置
  # 默认为服务名去除前缀转换为path 此处填特殊的配置
  service-mapping:
    naas-gen: /code



# seata配置
seata:
  # 是否启用
  enabled: false
  # Seata 应用编号，默认为应用名
  application-id: ${spring.application.name}
  # Seata 事务组编号，用于 TC 集群名
  tx-service-group: ${spring.application.name}-group
  service:
    vgroup-mapping:
      ${spring.application.name}-group: default

# actuator 监控配置
management:
  server:
    port: -1
  # 参数为通用属性，如web下修改path等
  endpoints:
    enabled: false
    enabled-by-default: false
    # 暴露 EndPoint 以供访问，有jmx和web两种方式，exclude 的优先级高于 include
    jmx:
      exposure:
        exclude: '*'
        include: '*'
    web:
      #作为默认路径，不配置默认路径为/actuator
      base-path: /actuator
      exposure:
        include: health
      #exposure:
      #  include: '*'
  # 参数是单个指定endpoint，如health，info等
  endpoint:
    auditevents: # 1、显示当前引用程序的审计事件信息，默认开启
      enabled: true
      cache:
        time-to-live: 10s # 配置端点缓存响应的时间
    beans: # 2、显示一个应用中所有 Spring Beans 的完整列表，默认开启
      enabled: true
    conditions: # 3、显示配置类和自动配置类的状态及它们被应用和未被应用的原因，默认开启
      enabled: true
    configprops: # 4、显示一个所有@ConfigurationProperties的集合列表，默认开启
      enabled: true
    env: # 5、显示来自Spring的 ConfigurableEnvironment的属性，默认开启
      enabled: true
    flyway: # 6、显示数据库迁移路径，如果有的话，默认开启
      enabled: true
    health: # 7、显示健康信息，默认开启
      enabled: false
      show-details: always
    info: # 8、显示任意的应用信息，默认开启
      enabled: true
    liquibase: # 9、展示任何Liquibase数据库迁移路径，如果有的话，默认开启
      enabled: true
    metrics: # 10、展示当前应用的metrics信息，默认开启
      enabled: true
    mappings: # 11、显示一个所有@RequestMapping路径的集合列表，默认开启
      enabled: true
    scheduledtasks: # 12、显示应用程序中的计划任务，默认开启
      enabled: true
    sessions: # 13、允许从Spring会话支持的会话存储中检索和删除(retrieval and deletion)用户会话。使用Spring Session对反应性Web应用程序的支持时不可用。默认开启。
      enabled: true
    shutdown: # 14、允许应用以优雅的方式关闭，默认关闭
      enabled: true
    threaddump: # 15、执行一个线程dump
      enabled: true
    # web 应用时可以使用以下端点
    heapdump: # 16、    返回一个GZip压缩的hprof堆dump文件，默认开启
      enabled: true
    jolokia: # 17、通过HTTP暴露JMX beans（当Jolokia在类路径上时，WebFlux不可用），默认开启
      enabled: true
      external-file: ./logs/${spring.application.name}/console.log
    logfile: # 18、返回日志文件内容（如果设置了logging.file或logging.path属性的话），支持使用HTTP Range头接收日志文件内容的部分信息，默认开启
      enabled: true
    prometheus: #19、以可以被Prometheus服务器抓取的格式显示metrics信息，默认开启
      enabled: true
    gateway:
      enabled: true

  metrics:
    tags:
      application: ${spring.application.name}
    export:
      prometheus:
        pushgateway:
          #pushgateway地址
          baseUrl: ***********:9091
          #推送周期
          pushRate: 15s
          #job定义名
          job : ${spring.application.name}
          #启用推送
          enabled: false

# 存储服务基础配置
s3:
  endpoint: http://***********:9010
  accessKey: cmcc_minio
  secretKey: cmcc_minio
  region: us-east-1
  https: false
  maxConnections: 50
  bucketName: integration-test-bucket

  # 连接超时配置
  connectionTimeout: 5000
  socketTimeout: 10000
  requestTimeout: 30000

  # 基础重试配置
  retry:
    default:
      maxRetries: 2
      baseDelay: 500
      maxDelay: 5000
      strategy: EXPONENTIAL_BACKOFF
      enableJitter: true
      jitterFactor: 0.1

# 日志配置

# 管理端点配置

# Dubbo配置（如果需要）
