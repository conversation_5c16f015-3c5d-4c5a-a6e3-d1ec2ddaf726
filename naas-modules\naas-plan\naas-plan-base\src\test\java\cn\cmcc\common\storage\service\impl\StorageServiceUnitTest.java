package cn.cmcc.common.storage.service.impl;

import cn.cmcc.common.storage.config.RetryConfig;
import cn.cmcc.common.storage.config.RetryStrategy;
import cn.cmcc.common.storage.config.S3Properties;
import cn.cmcc.common.storage.service.EnhancedRetryExecutor;
import cn.cmcc.common.storage.service.RetryMetricsService;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StorageService 单元测试类
 * 不依赖Spring Boot完整启动，专注于业务逻辑测试
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class StorageServiceUnitTest {

    @Mock
    private AmazonS3 mockS3Client;

    @Mock
    private S3Properties mockS3Properties;

    @Mock
    private EnhancedRetryExecutor mockRetryExecutor;

    @Mock
    private RetryMetricsService mockMetricsService;

    @Mock
    private HttpServletResponse mockResponse;

    @Mock
    private S3Object mockS3Object;

    @Mock
    private S3ObjectInputStream mockInputStream;

    @Mock
    private ObjectMetadata mockObjectMetadata;

    @Mock
    private MultipartFile mockMultipartFile;

    @InjectMocks
    private StorageService storageService;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        when(mockS3Properties.getMaxRetries()).thenReturn(3);
        when(mockS3Properties.getBucketName()).thenReturn("test-bucket");
        when(mockS3Properties.getConnectionTimeout()).thenReturn(10000);
        when(mockS3Properties.getSocketTimeout()).thenReturn(50000);
        when(mockS3Properties.getRequestTimeout()).thenReturn(300000);

        // 使用反射设置私有字段，避免@PostConstruct初始化
        ReflectionTestUtils.setField(storageService, "client", mockS3Client);

        // 设置S3客户端的基本Mock行为
        when(mockS3Client.doesBucketExistV2(anyString())).thenReturn(true);
        // createBucket返回Bucket对象，不是void方法
        when(mockS3Client.createBucket(anyString())).thenReturn(new com.amazonaws.services.s3.model.Bucket("test-bucket"));
    }

    // ==================== 基础功能测试 ====================

    @Test
    void testUploadWithRetryExecutor_Success() throws Exception {
        // 准备测试数据
        byte[] testData = "test file content".getBytes();
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("test.txt");

        PutObjectResult mockResult = new PutObjectResult();
        mockResult.setContentMd5("test-md5");

        // 模拟增强重试执行器的行为
        when(mockRetryExecutor.executeWithRetry(eq("upload"), any(), anyString()))
                .thenReturn(mockResult);

        // 执行测试
        String result = storageService.upload(mockMultipartFile);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("test.txt"));
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("upload"), any(), anyString());
    }

    @Test
    void testDownloadWithRetryExecutor_Success() throws Exception {
        // 准备测试数据
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockResponse.getOutputStream()).thenReturn(mock(ServletOutputStream.class));
        when(mockResponse.isCommitted()).thenReturn(false);

        // 模拟重试执行器
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);

        // 执行测试
        storageService.downLoad("test-file.txt", mockResponse);

        // 验证结果
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    @Test
    void testDownloadToOutputStream_Success() throws Exception {
        // 准备测试数据
        OutputStream testOutputStream = new ByteArrayOutputStream();
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);

        // 模拟重试执行器
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);

        // 执行测试
        storageService.downLoad("test-file.txt", testOutputStream);

        // 验证结果
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    @Test
    void testDownloadToLocalFile_Success() throws Exception {
        // 准备测试数据
        String localFilePath = "test-local-file.txt";
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);

        // 模拟重试执行器
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);

        // 执行测试
        storageService.downLoad("test-file.txt", localFilePath);

        // 验证结果
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    @Test
    void testGetObjectMetadata_Success() throws Exception {
        // 准备测试数据
        when(mockS3Object.getObjectMetadata()).thenReturn(mockObjectMetadata);

        // 模拟重试执行器
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);

        // 执行测试
        ObjectMetadata result = storageService.getObjectMetadata("/test-bucket/test-file.txt");

        // 验证结果
        assertNotNull(result);
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    @Test
    void testCheckFileDownloadable_Success() throws Exception {
        // 准备测试数据
        when(mockObjectMetadata.getContentLength()).thenReturn(1024L);
        when(mockS3Object.getObjectMetadata()).thenReturn(mockObjectMetadata);
        when(mockS3Object.getObjectContent()).thenReturn(mockInputStream);
        when(mockInputStream.read(any(byte[].class))).thenReturn(100);

        // 模拟重试执行器
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);

        // 执行测试
        boolean result = storageService.checkFileDownloadable("test-file.txt");

        // 验证结果
        assertTrue(result);
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    @Test
    void testCheckFileDownloadable_EmptyFile() throws Exception {
        // 准备测试数据 - 文件大小为0
        when(mockObjectMetadata.getContentLength()).thenReturn(0L);
        when(mockS3Object.getObjectMetadata()).thenReturn(mockObjectMetadata);

        // 模拟重试执行器
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenReturn(mockS3Object);

        // 执行测试
        boolean result = storageService.checkFileDownloadable("empty-file.txt");

        // 验证结果
        assertFalse(result);
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    // ==================== 异常处理测试 ====================

    @Test
    void testUploadWithRetryExecutor_Exception() throws Exception {
        // 准备测试数据
        byte[] testData = "test".getBytes();
        when(mockMultipartFile.getBytes()).thenReturn(testData);
        when(mockMultipartFile.getOriginalFilename()).thenReturn("test.txt");

        // 模拟重试执行器抛出异常
        when(mockRetryExecutor.executeWithRetry(eq("upload"), any(), anyString()))
                .thenThrow(new RuntimeException("上传失败"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            storageService.upload(mockMultipartFile);
        });

        assertTrue(exception.getMessage().contains("上传失败"));
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("upload"), any(), anyString());
    }

    @Test
    void testDownloadWithRetryExecutor_Exception() throws Exception {
        // 模拟重试执行器抛出异常
        when(mockRetryExecutor.executeWithRetry(eq("download"), any(), anyString()))
                .thenThrow(new RuntimeException("下载失败"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            storageService.downLoad("test-file.txt", mockResponse);
        });

        assertTrue(exception.getMessage().contains("下载失败"));
        verify(mockRetryExecutor, times(1)).executeWithRetry(eq("download"), any(), anyString());
    }

    // ==================== 重试策略测试 ====================

    @Test
    void testRetryStrategy_ExponentialBackoff() {
        RetryStrategy strategy = RetryStrategy.EXPONENTIAL_BACKOFF;

        // 测试指数退避计算
        long delay1 = strategy.calculateDelay(1, 1000, 30000, 0.8);
        long delay2 = strategy.calculateDelay(2, 1000, 30000, 0.8);
        long delay3 = strategy.calculateDelay(3, 1000, 30000, 0.8);

        assertEquals(1000, delay1);
        assertEquals(2000, delay2);
        assertEquals(4000, delay3);

        // 测试最大延迟限制
        long delayMax = strategy.calculateDelay(10, 1000, 5000, 0.8);
        assertEquals(5000, delayMax);
    }

    @Test
    void testRetryStrategy_LinearBackoff() {
        RetryStrategy strategy = RetryStrategy.LINEAR_BACKOFF;

        // 测试线性退避计算
        long delay1 = strategy.calculateDelay(1, 1000, 30000, 0.8);
        long delay2 = strategy.calculateDelay(2, 1000, 30000, 0.8);
        long delay3 = strategy.calculateDelay(3, 1000, 30000, 0.8);

        assertEquals(1000, delay1);
        assertEquals(2000, delay2);
        assertEquals(3000, delay3);
    }

    @Test
    void testRetryStrategy_FixedDelay() {
        RetryStrategy strategy = RetryStrategy.FIXED_DELAY;

        // 测试固定延迟计算
        long delay1 = strategy.calculateDelay(1, 1000, 30000, 0.8);
        long delay2 = strategy.calculateDelay(2, 1000, 30000, 0.8);
        long delay3 = strategy.calculateDelay(3, 1000, 30000, 0.8);

        assertEquals(1000, delay1);
        assertEquals(1000, delay2);
        assertEquals(1000, delay3);
    }

    @Test
    void testRetryStrategy_Adaptive() {
        RetryStrategy strategy = RetryStrategy.ADAPTIVE;

        // 测试自适应延迟计算
        // 高成功率场景
        long delayHighSuccess = strategy.calculateDelay(2, 1000, 30000, 0.9);
        assertEquals(2000, delayHighSuccess); // 2 * 1000 * 1.0

        // 中等成功率场景
        long delayMediumSuccess = strategy.calculateDelay(2, 1000, 30000, 0.7);
        assertEquals(3000, delayMediumSuccess); // 2 * 1000 * 1.5

        // 低成功率场景
        long delayLowSuccess = strategy.calculateDelay(2, 1000, 30000, 0.3);
        assertEquals(4000, delayLowSuccess); // 2 * 1000 * 2.0
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的输出流
     */
    private OutputStream createTestOutputStream() {
        return new ByteArrayOutputStream();
    }

    /**
     * 创建测试用的文件内容
     */
    private byte[] createTestFileContent() {
        return "This is unit test file content".getBytes();
    }
}
