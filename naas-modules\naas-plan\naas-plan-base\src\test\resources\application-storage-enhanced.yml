# 增强的存储服务配置
s3:
  # 基础S3配置
  endpoint: ${S3_ENDPOINT:http://10.18.18.37:9010}
  accessKey: ${S3_ACCESS_KEY:cmcc_minio}
  secretKey: ${S3_SECRET_KEY:cmcc_minio}
  region: ${S3_REGION:us-east-1}
  https: ${S3_HTTPS:false}
  maxConnections: ${S3_MAX_CONNECTIONS:200}
  bucketName: ${S3_BUCKET_NAME:integration-test-bucket}

  # 连接超时配置
  connectionTimeout: ${S3_CONNECTION_TIMEOUT:10000}
  socketTimeout: ${S3_SOCKET_TIMEOUT:50000}
  requestTimeout: ${S3_REQUEST_TIMEOUT:300000}

  # 连接池配置
  maxIdleTime: ${S3_MAX_IDLE_TIME:60000}
  validateAfterInactivity: ${S3_VALIDATE_AFTER_INACTIVITY:5000}

  # HTTP配置
  useGzip: ${S3_USE_GZIP:true}
  useExpectContinue: ${S3_USE_EXPECT_CONTINUE:false}

  # 增强重试配置
  retry:
    # 默认重试配置
    default:
      maxRetries: ${S3_RETRY_MAX_RETRIES:3}
      baseDelay: ${S3_RETRY_BASE_DELAY:1000}
      maxDelay: ${S3_RETRY_MAX_DELAY:30000}
      strategy: ${S3_RETRY_STRATEGY:EXPONENTIAL_BACKOFF}
      enableJitter: ${S3_RETRY_ENABLE_JITTER:true}
      jitterFactor: ${S3_RETRY_JITTER_FACTOR:0.1}

    # 按操作类型的重试配置
    operationSpecific:
      upload:
        maxRetries: ${S3_UPLOAD_MAX_RETRIES:5}
        baseDelay: ${S3_UPLOAD_BASE_DELAY:2000}
        maxDelay: ${S3_UPLOAD_MAX_DELAY:60000}
        strategy: ${S3_UPLOAD_STRATEGY:EXPONENTIAL_BACKOFF}
        enableJitter: true
        jitterFactor: 0.15

      download:
        maxRetries: ${S3_DOWNLOAD_MAX_RETRIES:3}
        baseDelay: ${S3_DOWNLOAD_BASE_DELAY:1000}
        maxDelay: ${S3_DOWNLOAD_MAX_DELAY:30000}
        strategy: ${S3_DOWNLOAD_STRATEGY:EXPONENTIAL_BACKOFF}
        enableJitter: true
        jitterFactor: 0.1

      metadata:
        maxRetries: ${S3_METADATA_MAX_RETRIES:2}
        baseDelay: ${S3_METADATA_BASE_DELAY:500}
        maxDelay: ${S3_METADATA_MAX_DELAY:10000}
        strategy: ${S3_METADATA_STRATEGY:LINEAR_BACKOFF}
        enableJitter: false

    # 按错误类型的重试配置
    errorSpecific:
      connection_timeout:
        maxRetries: 5
        baseDelay: 2000
        strategy: EXPONENTIAL_BACKOFF

      read_timeout:
        maxRetries: 3
        baseDelay: 1500
        strategy: LINEAR_BACKOFF

      socket_timeout:
        maxRetries: 4
        baseDelay: 1000
        strategy: EXPONENTIAL_BACKOFF

      connection_refused:
        maxRetries: 6
        baseDelay: 3000
        strategy: EXPONENTIAL_BACKOFF

      network_unreachable:
        maxRetries: 8
        baseDelay: 5000
        strategy: FIXED_DELAY
        fixedDelay: 5000

      aws_service_InternalError:
        maxRetries: 3
        baseDelay: 2000
        strategy: EXPONENTIAL_BACKOFF

      aws_service_ServiceUnavailable:
        maxRetries: 10
        baseDelay: 5000
        strategy: FIXED_DELAY
        fixedDelay: 5000

    # 自适应配置
    adaptive:
      successRateThreshold: ${S3_ADAPTIVE_SUCCESS_RATE_THRESHOLD:0.8}
      adjustmentFactor: ${S3_ADAPTIVE_ADJUSTMENT_FACTOR:1.5}
      windowSize: ${S3_ADAPTIVE_WINDOW_SIZE:100}
      minSampleSize: ${S3_ADAPTIVE_MIN_SAMPLE_SIZE:10}

    # 熔断器配置
    enableCircuitBreaker: ${S3_ENABLE_CIRCUIT_BREAKER:false}
    circuitBreaker:
      failureRateThreshold: ${S3_CB_FAILURE_RATE_THRESHOLD:0.5}
      minimumNumberOfCalls: ${S3_CB_MIN_CALLS:10}
      waitDurationInOpenState: ${S3_CB_WAIT_DURATION:60000}
      permittedNumberOfCallsInHalfOpenState: ${S3_CB_HALF_OPEN_CALLS:3}
      slidingWindowSize: ${S3_CB_SLIDING_WINDOW_SIZE:100}

# 日志配置
logging:
  level:
    cn.cmcc.common.storage: ${STORAGE_LOG_LEVEL:INFO}
    cn.cmcc.common.storage.service.RetryMetricsService: ${RETRY_METRICS_LOG_LEVEL:INFO}
    cn.cmcc.common.storage.service.EnhancedRetryExecutor: ${RETRY_EXECUTOR_LOG_LEVEL:INFO}

# 管理端点配置（用于监控重试指标）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,storage-retry-metrics
  endpoint:
    health:
      show-details: always
    storage-retry-metrics:
      enabled: true

# 环境特定配置
---
# 开发环境配置
spring:
  profiles: dev

s3:
  retry:
    default:
      maxRetries: 1
      baseDelay: 500
    operationSpecific:
      upload:
        maxRetries: 2
        baseDelay: 1000
      download:
        maxRetries: 1
        baseDelay: 500

logging:
  level:
    cn.cmcc.common.storage: DEBUG

---
# 测试环境配置
spring:
  profiles: test

s3:
  retry:
    default:
      maxRetries: 2
      baseDelay: 1000
    enableCircuitBreaker: false

---
# 生产环境配置
spring:
  profiles: prod

s3:
  retry:
    default:
      maxRetries: 5
      baseDelay: 2000
      maxDelay: 60000
    operationSpecific:
      upload:
        maxRetries: 8
        baseDelay: 3000
        maxDelay: 120000
      download:
        maxRetries: 5
        baseDelay: 2000
        maxDelay: 60000
    enableCircuitBreaker: true
    circuitBreaker:
      failureRateThreshold: 0.6
      minimumNumberOfCalls: 20
      waitDurationInOpenState: 120000

logging:
  level:
    cn.cmcc.common.storage: WARN
    cn.cmcc.common.storage.service.RetryMetricsService: INFO
