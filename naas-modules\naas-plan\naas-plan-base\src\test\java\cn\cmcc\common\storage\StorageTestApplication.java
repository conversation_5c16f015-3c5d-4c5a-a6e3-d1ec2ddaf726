package cn.cmcc.common.storage;

import cn.cmcc.common.storage.config.StorageTestConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

/**
 * 存储服务测试启动类
 * 用于Spring Boot测试环境
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-30
 */
@SpringBootApplication(scanBasePackages = {
    "cn.cmcc.common.storage",
    "cn.cmcc.plan"
})
@Import(StorageTestConfiguration.class)
public class StorageTestApplication {

    public static void main(String[] args) {
        SpringApplication.run(StorageTestApplication.class, args);
    }
}
